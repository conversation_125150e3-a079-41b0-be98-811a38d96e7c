import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import legacy from '@vitejs/plugin-legacy'
import autoprefixer from 'autoprefixer'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import { visualizer } from 'rollup-plugin-visualizer'
import viteCompression from 'vite-plugin-compression'
import { rewriteKeepAlive } from 'sinitek-util/dist/plugins'
import fs from 'fs'
import path from 'path'

export default defineConfig(({ mode }) => {
  // 支持通过环境变量指定配置文件
  const envFile = process.env.VITE_ENV_FILE;
  let env;

  if (envFile) {
    // 手动加载指定的环境文件
    const envPath = path.resolve(__dirname, envFile);

    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf-8');
      env = {};

      // 解析环境文件
      envContent.split('\n').forEach(line => {
        const trimmedLine = line.trim();
        if (trimmedLine && !trimmedLine.startsWith('#')) {
          const [key, ...valueParts] = trimmedLine.split('=');
          if (key && valueParts.length > 0) {
            const value = valueParts.join('=').trim();
            env[key.trim()] = value;
          }
        }
      });

      console.log(`✅ 加载环境配置: ${envFile}`);
    } else {
      console.warn(`⚠️ 环境文件不存在: ${envFile}，使用默认配置`);
      env = loadEnv(mode, __dirname);
    }
  } else {
    // 使用默认的环境加载方式
    env = loadEnv(mode, __dirname);
  }

  return {
    // 公共基础路径
    base: env.VITE_BASE,
    // 开发服务器配置
    server: {
      port: 5173,
      proxy: {
        // 代理请求
        '/frontend': {
          target: env.VITE_PROXY_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\//, '')
        },
        '/preview': {
          target: env.VITE_FILE_PREVIEW_PROXY_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\//, '')
        }
      }
    },
    resolve: {
      // 别名配置
      alias: {
        '@': path.join(__dirname, 'src'),
        src: path.join(__dirname, 'src'),
        '@assets': path.join(__dirname, 'src/assets'),
        // 处理esm-bundler警告
        'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js'
      }
    },
    optimizeDeps: {
      include: ['sinitek-util', 'sinitek-ui', 'sinitek-workflow', 'sinitek-message', 'sirmapp'],
      exclude: ['vue']
    },
    // css 相关的配置
    css: {
      // 进行 PostCSS 配置
      postcss: {
        plugins: [
          autoprefixer({
            // 指定目标浏览器
            overrideBrowserslist: ['Chrome > 40', 'ff > 31', '> 1%']
          })
        ]
      }
    },
    // 插件配置
    plugins: [
      vue(),
      vueJsx(),
      viteCompression(),
      // 浏览器兼容，库模式下不可用
      legacy({
        targets: ['defaults', 'not IE 11']
      }),
      // svg 雪碧图
      createSvgIconsPlugin({
        // 指定需要缓存的图标文件夹
        iconDirs: [path.resolve(__dirname, 'src/icons/svg')],
        customDomId: '__' + path.basename(path.resolve()) + '__svg__icons__dom__'
      }),
      rewriteKeepAlive(),
      visualizer({
        filename: 'dist/report.html',
        // open: true,
        gzipSize: true
      })
    ],
    // 生产打包配置
    build: {
      // 静态资源目录名称
      assetsDir: 'static',
      sourcemap: false,
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: false,
          drop_debugger: true,
          pure_funcs: ['console.log']
        },
        format: {
          comments: false
        }
      },
      rollupOptions: {
        output: {
          manualChunks: {
            'vue-render': ['vue', 'vuex', 'vue-router', 'vue-i18n'],
            'element-plus': ['element-plus'],
            sirmapp: ['sirmapp'],
            'sinitek-ui': ['sinitek-ui'],
            'sinitek-util': ['sinitek-util'],
            'sinitek-message': ['sinitek-message'],
            'sinitek-workflow': ['sinitek-workflow'],
            'vue-grid-layout': ['vue-grid-layout']
          }
        }
      }
    }
  }
})
