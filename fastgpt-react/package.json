{"name": "sinitek-mind-react-components", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:check": "tsc && vite build", "copy:dist": "node -e \"const fs=require('fs'); const path=require('path'); if(fs.existsSync('dist')) { if(fs.existsSync('../dist/gpt')) fs.rmSync('../dist/gpt', {recursive: true}); fs.cpSync('dist', '../dist/gpt', {recursive: true}); }\"", "preview": "vite preview", "serve": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@apidevtools/swagger-parser": "^10.1.0", "@bany/curl-to-json": "^1.2.8", "@chakra-ui/anatomy": "2.2.1", "@chakra-ui/icons": "2.1.1", "@chakra-ui/react": "2.10.7", "@chakra-ui/styled-system": "2.9.1", "@chakra-ui/system": "2.6.1", "@dagrejs/dagre": "^1.1.4", "@emotion/react": "11.11.1", "@emotion/styled": "11.11.0", "@fingerprintjs/fingerprintjs": "^4.3.0", "@fortaine/fetch-event-source": "^3.0.6", "@lexical/react": "0.12.6", "@lexical/selection": "^0.14.5", "@lexical/text": "0.12.6", "@lexical/utils": "0.12.6", "@modelcontextprotocol/sdk": "1.12.0", "@monaco-editor/react": "^4.6.0", "@node-rs/jieba": "2.0.1", "@tanstack/react-query": "^4.24.10", "ahooks": "^3.7.11", "axios": "^1.8.2", "cron-parser": "^4.9.0", "crypto-js": "^4.2.0", "date-fns": "2.30.0", "dayjs": "^1.11.7", "echarts": "5.4.1", "echarts-gl": "2.0.9", "encoding": "^0.1.13", "framer-motion": "9.1.7", "hyperdown": "^2.4.29", "i18next": "23.16.8", "immer": "^9.0.19", "js-cookie": "^3.0.5", "js-yaml": "^4.1.0", "jschardet": "3.1.1", "json5": "^2.2.3", "jsondiffpatch": "^0.6.0", "jsonwebtoken": "^9.0.2", "katex": "^0.16.22", "lexical": "0.12.6", "lodash": "^4.17.21", "mermaid": "^10.2.3", "nanoid": "^5.1.3", "nprogress": "^0.2.0", "openai": "4.61.0", "openapi-types": "^12.1.3", "papaparse": "^5.4.1", "qrcode": "^1.5.4", "react": "18.3.1", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "^8.7.1", "react-dom": "18.3.1", "react-hook-form": "7.43.1", "react-i18next": "14.1.2", "react-markdown": "^9.0.1", "react-photo-view": "^1.2.6", "react-router-dom": "^6.26.1", "react-syntax-highlighter": "^15.5.0", "react-textarea-autosize": "^8.5.4", "reactflow": "^11.7.4", "recharts": "^2.15.0", "rehype-external-links": "^3.0.0", "rehype-katex": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "sass": "^1.58.3", "timezones-list": "^3.0.2", "use-context-selector": "^1.4.4", "zod": "^3.24.2", "zustand": "^4.3.5"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.5", "@types/js-yaml": "^4.0.9", "@types/jsonwebtoken": "^9.0.3", "@types/lodash": "^4.14.191", "@types/node": "^20.14.2", "@types/nprogress": "^0.2.0", "@types/papaparse": "^5.3.7", "@types/qrcode": "^1.5.5", "@types/react": "18.3.1", "@types/react-beautiful-dnd": "^13.1.1", "@types/react-dom": "18.3.0", "@types/react-router-dom": "^5.3.3", "@types/react-syntax-highlighter": "^15.5.6", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.7", "typescript": "^5.1.3", "vite": "^5.4.1", "vite-plugin-svgr": "^4.2.0"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0", "@chakra-ui/react": "^2.0.0", "@emotion/react": "^11.0.0", "@emotion/styled": "^11.0.0", "framer-motion": "^9.0.0", "react-router-dom": "^6.0.0"}}