# 依赖目录
node_modules
.pnpm-store

# Next.js 构建输出
.next
out

# 开发工具
.vscode
.idea

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时文件
.env*.local
.env.local
.env.development.local
.env.test.local
.env.production.local

# 测试覆盖率
coverage

# 临时文件
.tmp
*.tmp
*.temp

# 操作系统文件
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile
.dockerignore

# 文档
README.md
*.md

# 其他
.eslintcache 