/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/define("vs/editor/editor.main.nls.es",{"vs/base/browser/ui/actionbar/actionViewItems":["{0} ({1})"],"vs/base/browser/ui/findinput/findInput":["entrada"],"vs/base/browser/ui/findinput/findInputToggles":["Coincidir may\xFAsculas y min\xFAsculas","Solo palabras completas","Usar expresi\xF3n regular"],"vs/base/browser/ui/findinput/replaceInput":["entrada","Conservar may/min"],"vs/base/browser/ui/hover/hoverWidget":["Inspeccione esto en la vista accesible con {0}.","Inspeccione esto en la vista accesible mediante el comando Abrir vista accesible, que actualmente no se puede desencadenar mediante el enlace de teclado."],"vs/base/browser/ui/iconLabel/iconLabelHover":["Cargando..."],"vs/base/browser/ui/inputbox/inputBox":["Error: {0}","Advertencia: {0}","Informaci\xF3n: {0}"," o {0} para el historial"," ({0} para el historial)","Entrada borrada"],"vs/base/browser/ui/keybindingLabel/keybindingLabel":["Sin enlazar"],"vs/base/browser/ui/selectBox/selectBoxCustom":["Seleccionar cuadro"],"vs/base/browser/ui/toolbar/toolbar":["M\xE1s Acciones..."],"vs/base/browser/ui/tree/abstractTree":["Filtrar","Coincidencia aproximada","Escriba texto para filtrar","Escriba texto para buscar","Escriba texto para buscar","Cerrar","No se encontraron elementos."],"vs/base/common/actions":["(vac\xEDo)"],"vs/base/common/errorMessage":["{0}: {1}","Error del sistema ({0})","Se ha producido un error desconocido. Consulte el registro para obtener m\xE1s detalles.","Se ha producido un error desconocido. Consulte el registro para obtener m\xE1s detalles.","{0} ({1} errores en total)","Se ha producido un error desconocido. Consulte el registro para obtener m\xE1s detalles."],"vs/base/common/keybindingLabels":["Ctrl","May\xFAs","Alt","Windows","Ctrl","May\xFAs","Alt","Super","Control","May\xFAs","Opci\xF3n","Comando","Control","May\xFAs","Alt","Windows","Control","May\xFAs","Alt","Super"],"vs/base/common/platform":["_"],"vs/editor/browser/controller/textAreaHandler":["editor","No se puede acceder al editor en este momento.","{0} Para habilitar el modo optimizado para lectores de pantalla, use {1}","{0} Para habilitar el modo optimizado para lector de pantalla, abra la selecci\xF3n r\xE1pida con {1} y ejecute el comando Alternar modo de accesibilidad del lector de pantalla, que actualmente no se puede desencadenar mediante el teclado.","{0} Para asignar un enlace de teclado para el comando Alternar modo de accesibilidad del lector de pantalla, acceda al editor de enlaces de teclado con {1} y ejec\xFAtelo."],"vs/editor/browser/coreCommands":["Anclar al final incluso cuando se vayan a l\xEDneas m\xE1s largas","Anclar al final incluso cuando se vayan a l\xEDneas m\xE1s largas","Cursores secundarios quitados"],"vs/editor/browser/editorExtensions":["&&Deshacer","Deshacer","&&Rehacer","Rehacer","&&Seleccionar todo","Seleccionar todo"],"vs/editor/browser/widget/codeEditorWidget":["El n\xFAmero de cursores se ha limitado a {0}. Considere la posibilidad de usar [buscar y reemplazar](https://code.visualstudio.com/docs/editor/codebasics#_find-and-replace) para realizar cambios mayores o aumentar la configuraci\xF3n del l\xEDmite de varios cursores del editor.","Aumentar el l\xEDmite de varios cursores"],"vs/editor/browser/widget/diffEditor/accessibleDiffViewer":['Icono de "Insertar" en el visor de diferencias accesible.','Icono de "Quitar" en el visor de diferencias accesible.','Icono de "Cerrar" en el visor de diferencias accesible.',"Cerrar","Visor de diferencias accesible. Utilice la flecha hacia arriba y hacia abajo para navegar.","no se han cambiado l\xEDneas","1 l\xEDnea cambiada","{0} l\xEDneas cambiadas","Diferencia {0} de {1}: l\xEDnea original {2}, {3}, l\xEDnea modificada {4}, {5}","vac\xEDo","{0} l\xEDnea sin cambios {1}","{0} l\xEDnea original {1} l\xEDnea modificada {2}","+ {0} l\xEDnea modificada {1}","- {0} l\xEDnea original {1}"],"vs/editor/browser/widget/diffEditor/colors":["Color del borde del texto que se movi\xF3 en el editor de diferencias.","Color del borde de texto activo que se movi\xF3 en el editor de diferencias.","Color de la sombra paralela en torno a los widgets de regi\xF3n sin cambios."],"vs/editor/browser/widget/diffEditor/decorations":["Decoraci\xF3n de l\xEDnea para las inserciones en el editor de diferencias.","Decoraci\xF3n de l\xEDnea para las eliminaciones en el editor de diferencias."],"vs/editor/browser/widget/diffEditor/diffEditor.contribution":["Alternar contraer regiones sin cambios","Alternar Mostrar bloques de c\xF3digo movidos","Alternar el uso de la vista insertada cuando el espacio es limitado","Uso de la vista insertada cuando el espacio es limitado","Mostrar bloques de c\xF3digo movidos","Editor de diferencias","Lado del conmutador","Salir de la comparaci\xF3n de movimientos","Contraer todas las regiones sin cambios","Mostrar todas las regiones sin cambios","Visor de diferencias accesibles","Ir a la siguiente diferencia","Abrir visor de diferencias accesibles","Ir a la diferencia anterior"],"vs/editor/browser/widget/diffEditor/diffEditorDecorations":["Revertir los cambios seleccionados","Revertir el cambio"],"vs/editor/browser/widget/diffEditor/diffEditorEditors":[" use {0} para abrir la ayuda de accesibilidad."],"vs/editor/browser/widget/diffEditor/hideUnchangedRegionsFeature":["Plegar la regi\xF3n sin cambios","Haga clic o arrastre para mostrar m\xE1s arriba","Mostrar regi\xF3n sin cambios","Hacer clic o arrastrar para mostrar m\xE1s abajo","{0} l\xEDneas ocultas","Doble clic para desplegar"],"vs/editor/browser/widget/diffEditor/inlineDiffDeletedCodeMargin":["Copiar l\xEDneas eliminadas","Copiar l\xEDnea eliminada","Copiar l\xEDneas cambiadas","Copiar l\xEDnea cambiada","Copiar la l\xEDnea eliminada ({0})","Copiar l\xEDnea cambiada ({0})","Revertir este cambio"],"vs/editor/browser/widget/diffEditor/movedBlocksLines":["C\xF3digo movido con cambios en la l\xEDnea {0}-{1}","C\xF3digo movido con cambios de la l\xEDnea {0}-{1}","C\xF3digo movido a la l\xEDnea {0}-{1}","C\xF3digo movido de la l\xEDnea {0}-{1}"],"vs/editor/browser/widget/multiDiffEditorWidget/colors":["Color de fondo del encabezado del editor de diferencias"],"vs/editor/common/config/editorConfigurationSchema":["Editor","El n\xFAmero de espacios a los que equivale una tabulaci\xF3n. Este valor se invalida en funci\xF3n del contenido del archivo cuando {0} est\xE1 activado.",'N\xFAmero de espacios usados para la sangr\xEDa o "tabSize" para usar el valor de "#editor.tabSize#". Esta configuraci\xF3n se invalida en funci\xF3n del contenido del archivo cuando "#editor.detectIndentation#" est\xE1 activado.','Insertar espacios al presionar "TAB". Este valor se invalida en funci\xF3n del contenido del archivo cuando {0} est\xE1 activado.',"Controla si {0} y {1} se detectan autom\xE1ticamente al abrir un archivo en funci\xF3n del contenido de este.","Quitar el espacio en blanco final autoinsertado.","Manejo especial para archivos grandes para desactivar ciertas funciones de memoria intensiva.","Desactivar sugerencias basadas en Word.","Sugerir palabras solo del documento activo.","Sugerir palabras de todos los documentos abiertos del mismo idioma.","Sugerir palabras de todos los documentos abiertos.","Controla si las finalizaciones se deben calcular en funci\xF3n de las palabras del documento y desde qu\xE9 documentos se calculan.","El resaltado sem\xE1ntico est\xE1 habilitado para todos los temas de color.","El resaltado sem\xE1ntico est\xE1 deshabilitado para todos los temas de color.",'El resaltado sem\xE1ntico est\xE1 configurado con el valor "semanticHighlighting" del tema de color actual.',"Controla si se muestra semanticHighlighting para los idiomas que lo admiten.",'Mantiene abiertos los editores interactivos, incluso al hacer doble clic en su contenido o presionar "Escape".',"Las lineas por encima de esta longitud no se tokenizar\xE1n por razones de rendimiento.","Controla si la tokenizaci\xF3n debe producirse de forma asincr\xF3nica en un rol de trabajo.","Controla si se debe registrar la tokenizaci\xF3n asincr\xF3nica. Solo para depuraci\xF3n.","Controla si se debe comprobar la tokenizaci\xF3n asincr\xF3nica con la tokenizaci\xF3n en segundo plano heredada. Puede ralentizar la tokenizaci\xF3n. Solo para depuraci\xF3n.","Define los corchetes que aumentan o reducen la sangr\xEDa.","Secuencia de cadena o corchete de apertura.","Secuencia de cadena o corchete de cierre.","Define los pares de corchetes coloreados por su nivel de anidamiento si est\xE1 habilitada la coloraci\xF3n de par de corchetes.","Secuencia de cadena o corchete de apertura.","Secuencia de cadena o corchete de cierre.","Tiempo de espera en milisegundos despu\xE9s del cual se cancela el c\xE1lculo de diferencias. Utilice 0 para no usar tiempo de espera.","Tama\xF1o m\xE1ximo de archivo en MB para el que calcular diferencias. Use 0 para no limitar.","Controla si el editor de diferencias muestra las diferencias en paralelo o alineadas.","Si el ancho del editor de diferencias es menor que este valor, se usa la vista insertada.","Si est\xE1 habilitada y el ancho del editor es demasiado peque\xF1o, se usa la vista en l\xEDnea.","Cuando est\xE1 habilitado, el editor de diferencias muestra flechas en su margen de glifo para revertir los cambios.","Cuando est\xE1 habilitado, el editor de diferencias omite los cambios en los espacios en blanco iniciales o finales.","Controla si el editor de diferencias muestra los indicadores +/- para los cambios agregados o quitados.","Controla si el editor muestra CodeLens.","Las l\xEDneas no se ajustar\xE1n nunca.","Las l\xEDneas se ajustar\xE1n en el ancho de la ventanilla.","Las l\xEDneas se ajustar\xE1n en funci\xF3n de la configuraci\xF3n de {0}.","Usa el algoritmo de diferenciaci\xF3n heredado.","Usa el algoritmo de diferenciaci\xF3n avanzada.","Controla si el editor de diferencias muestra las regiones sin cambios.","Controla cu\xE1ntas l\xEDneas se usan para las regiones sin cambios.","Controla cu\xE1ntas l\xEDneas se usan como m\xEDnimo para las regiones sin cambios.","Controla cu\xE1ntas l\xEDneas se usan como contexto al comparar regiones sin cambios.","Controlar si el editor de diferencias debe mostrar los movimientos de c\xF3digo detectados.","Controla si el editor de diferencias muestra decoraciones vac\xEDas para ver d\xF3nde se insertan o eliminan los caracteres."],"vs/editor/common/config/editorOptions":["Usar las API de la plataforma para detectar cu\xE1ndo se conecta un lector de pantalla.","Optimizar para usar con un lector de pantalla.","Supongamos que no hay un lector de pantalla conectado.","Controla si la interfaz de usuario debe ejecutarse en un modo en el que est\xE9 optimizada para lectores de pantalla.","Controla si se inserta un car\xE1cter de espacio al comentar.","Controla si las l\xEDneas vac\xEDas deben ignorarse con la opci\xF3n de alternar, agregar o quitar acciones para los comentarios de l\xEDnea.","Controla si al copiar sin selecci\xF3n se copia la l\xEDnea actual.","Controla si el cursor debe saltar para buscar coincidencias mientras se escribe.","Nunca inicializar la cadena de b\xFAsqueda desde la selecci\xF3n del editor.","Siempre inicializar la cadena de b\xFAsqueda desde la selecci\xF3n del editor, incluida la palabra en la posici\xF3n del cursor.","Solo inicializar la cadena de b\xFAsqueda desde la selecci\xF3n del editor.","Controla si la cadena de b\xFAsqueda del widget de b\xFAsqueda se inicializa desde la selecci\xF3n del editor.","No activar nunca Buscar en selecci\xF3n autom\xE1ticamente (predeterminado).","Activar siempre Buscar en selecci\xF3n autom\xE1ticamente.","Activar Buscar en la selecci\xF3n autom\xE1ticamente cuando se seleccionen varias l\xEDneas de contenido.","Controla la condici\xF3n para activar la b\xFAsqueda en la selecci\xF3n de forma autom\xE1tica.","Controla si el widget de b\xFAsqueda debe leer o modificar el Portapapeles de b\xFAsqueda compartido en macOS.","Controla si Encontrar widget debe agregar m\xE1s l\xEDneas en la parte superior del editor. Si es true, puede desplazarse m\xE1s all\xE1 de la primera l\xEDnea cuando Encontrar widget est\xE1 visible.","Controla si la b\xFAsqueda se reinicia autom\xE1ticamente desde el principio (o el final) cuando no se encuentran m\xE1s coincidencias.",'Habilita o deshabilita las ligaduras tipogr\xE1ficas (caracter\xEDsticas de fuente "calt" y "liga"). C\xE1mbielo a una cadena para el control espec\xEDfico de la propiedad de CSS "font-feature-settings".','Propiedad de CSS "font-feature-settings" expl\xEDcita. En su lugar, puede pasarse un valor booleano si solo es necesario activar o desactivar las ligaduras.','Configura las ligaduras tipogr\xE1ficas o las caracter\xEDsticas de fuente. Puede ser un valor booleano para habilitar o deshabilitar las ligaduras o bien una cadena para el valor de la propiedad "font-feature-settings" de CSS.',"Habilita o deshabilita la traducci\xF3n del grosor de font-weight a font-variation-settings. Cambie esto a una cadena para el control espec\xEDfico de la propiedad CSS 'font-variation-settings'.","Propiedad CSS expl\xEDcita 'font-variation-settings'. En su lugar, se puede pasar un valor booleano si solo es necesario traducir font-weight a font-variation-settings.","Configura variaciones de fuente. Puede ser un booleano para habilitar o deshabilitar la traducci\xF3n de font-weight a font-variation-settings o una cadena para el valor de la propiedad CSS 'font-variation-settings'.","Controla el tama\xF1o de fuente en p\xEDxeles.",'Solo se permiten las palabras clave "normal" y "negrita" o los n\xFAmeros entre 1 y 1000.','Controla el grosor de la fuente. Acepta las palabras clave "normal" y "negrita" o los n\xFAmeros entre 1 y 1000.',"Mostrar vista de inspecci\xF3n de los resultados (predeterminado)","Ir al resultado principal y mostrar una vista de inspecci\xF3n","Vaya al resultado principal y habilite la navegaci\xF3n sin peek para otros",'Esta configuraci\xF3n est\xE1 en desuso. Use configuraciones separadas como "editor.editor.gotoLocation.multipleDefinitions" o "editor.editor.gotoLocation.multipleImplementations" en su lugar.','Controla el comportamiento del comando "Ir a definici\xF3n" cuando existen varias ubicaciones de destino.','Controla el comportamiento del comando "Ir a definici\xF3n de tipo" cuando existen varias ubicaciones de destino.','Controla el comportamiento del comando "Ir a declaraci\xF3n" cuando existen varias ubicaciones de destino.','Controla el comportamiento del comando "Ir a implementaciones" cuando existen varias ubicaciones de destino.','Controla el comportamiento del comando "Ir a referencias" cuando existen varias ubicaciones de destino.','Identificador de comando alternativo que se ejecuta cuando el resultado de "Ir a definici\xF3n" es la ubicaci\xF3n actual.','Id. de comando alternativo que se est\xE1 ejecutando cuando el resultado de "Ir a definici\xF3n de tipo" es la ubicaci\xF3n actual.','Id. de comando alternativo que se est\xE1 ejecutando cuando el resultado de "Ir a declaraci\xF3n" es la ubicaci\xF3n actual.','Id. de comando alternativo que se est\xE1 ejecutando cuando el resultado de "Ir a implementaci\xF3n" es la ubicaci\xF3n actual.','Identificador de comando alternativo que se ejecuta cuando el resultado de "Ir a referencia" es la ubicaci\xF3n actual.',"Controla si se muestra la informaci\xF3n al mantener el puntero sobre un elemento.","Controla el retardo en milisegundos despu\xE9s del cual se muestra la informaci\xF3n al mantener el puntero sobre un elemento.","Controla si la informaci\xF3n que aparece al mantener el puntero sobre un elemento permanece visible al mover el mouse sobre este.","Controla el retraso en milisegundos despu\xE9s del cual se oculta el desplazamiento. Requiere que se habilite `editor.hover.sticky`.","Preferir mostrar los desplazamientos por encima de la l\xEDnea, si hay espacio.","Se supone que todos los caracteres son del mismo ancho. Este es un algoritmo r\xE1pido que funciona correctamente para fuentes monoespaciales y ciertos scripts (como caracteres latinos) donde los glifos tienen el mismo ancho.","Delega el c\xE1lculo de puntos de ajuste en el explorador. Es un algoritmo lento, que podr\xEDa causar bloqueos para archivos grandes, pero funciona correctamente en todos los casos.","Controla el algoritmo que calcula los puntos de ajuste. Tenga en cuenta que, en el modo de accesibilidad, se usar\xE1 el modo avanzado para obtener la mejor experiencia.","Habilita la bombilla de acci\xF3n de c\xF3digo en el editor.","No mostrar el icono de IA.","Muestra un icono de IA cuando el men\xFA de acci\xF3n de c\xF3digo contiene una acci\xF3n de IA, pero solo en c\xF3digo.","Muestra un icono de IA cuando el men\xFA de acci\xF3n de c\xF3digo contiene una acci\xF3n de IA, en c\xF3digo y l\xEDneas vac\xEDas.","Muestra un icono de IA junto con la bombilla cuando el men\xFA de acci\xF3n de c\xF3digo contiene una acci\xF3n de IA.","Muestra los \xE1mbitos actuales anidados durante el desplazamiento en la parte superior del editor.","Define el n\xFAmero m\xE1ximo de l\xEDneas r\xE1pidas que se mostrar\xE1n.","Define el modelo que se va a usar para determinar qu\xE9 l\xEDneas se van a pegar. Si el modelo de esquema no existe, recurrir\xE1 al modelo del proveedor de plegado que recurre al modelo de sangr\xEDa. Este orden se respeta en los tres casos.","Habilite el desplazamiento de desplazamiento r\xE1pido con la barra de desplazamiento horizontal del editor.","Habilita las sugerencias de incrustaci\xF3n en el editor.","Las sugerencias de incrustaci\xF3n est\xE1n habilitadas","Las sugerencias de incrustaci\xF3n se muestran de forma predeterminada y se ocultan cuando se mantiene presionado {0}","Las sugerencias de incrustaci\xF3n est\xE1n ocultas de forma predeterminada y se muestran al mantener presionado {0}","Las sugerencias de incrustaci\xF3n est\xE1n deshabilitadas","Controla el tama\xF1o de fuente de las sugerencias de incrustaci\xF3n en el editor. Como valor predeterminado, se usa {0} cuando el valor configurado es menor que {1} o mayor que el tama\xF1o de fuente del editor.","Controla la familia de fuentes de sugerencias de incrustaci\xF3n en el editor. Cuando se establece en vac\xEDo, se usa el {0}.","Habilita el relleno alrededor de las sugerencias de incrustaci\xF3n en el editor.",`Controla el alto de l\xEDnea. \r
 - Use 0 para calcular autom\xE1ticamente el alto de l\xEDnea a partir del tama\xF1o de la fuente.\r
 - Los valores entre 0 y 8 se usar\xE1n como multiplicador con el tama\xF1o de fuente.\r
 - Los valores mayores o igual que 8 se usar\xE1n como valores efectivos.`,"Controla si se muestra el minimapa.","Controla si el minimapa se oculta autom\xE1ticamente.","El minimapa tiene el mismo tama\xF1o que el contenido del editor (y podr\xEDa desplazarse).","El minimapa se estirar\xE1 o reducir\xE1 seg\xFAn sea necesario para ocupar la altura del editor (sin desplazamiento).","El minimapa se reducir\xE1 seg\xFAn sea necesario para no ser nunca m\xE1s grande que el editor (sin desplazamiento).","Controla el tama\xF1o del minimapa.","Controla en qu\xE9 lado se muestra el minimapa.","Controla cu\xE1ndo se muestra el control deslizante del minimapa.","Escala del contenido dibujado en el minimapa: 1, 2 o 3.","Represente los caracteres reales en una l\xEDnea, por oposici\xF3n a los bloques de color.","Limite el ancho del minimapa para representar como mucho un n\xFAmero de columnas determinado.","Controla la cantidad de espacio entre el borde superior del editor y la primera l\xEDnea.","Controla el espacio entre el borde inferior del editor y la \xFAltima l\xEDnea.","Habilita un elemento emergente que muestra documentaci\xF3n de los par\xE1metros e informaci\xF3n de los tipos mientras escribe.","Controla si el men\xFA de sugerencias de par\xE1metros se cicla o se cierra al llegar al final de la lista.","Las sugerencias r\xE1pidas se muestran dentro del widget de sugerencias","Las sugerencias r\xE1pidas se muestran como texto fantasma","Las sugerencias r\xE1pidas est\xE1n deshabilitadas","Habilita sugerencias r\xE1pidas en las cadenas.","Habilita sugerencias r\xE1pidas en los comentarios.","Habilita sugerencias r\xE1pidas fuera de las cadenas y los comentarios.","Controla si las sugerencias deben mostrarse autom\xE1ticamente al escribir. Puede controlarse para la escritura en comentarios, cadenas y otro c\xF3digo. Las sugerencias r\xE1pidas pueden configurarse para mostrarse como texto fantasma o con el widget de sugerencias. Tenga tambi\xE9n en cuenta la configuraci\xF3n '{0}' que controla si las sugerencias son desencadenadas por caracteres especiales.","Los n\xFAmeros de l\xEDnea no se muestran.","Los n\xFAmeros de l\xEDnea se muestran como un n\xFAmero absoluto.","Los n\xFAmeros de l\xEDnea se muestran como distancia en l\xEDneas a la posici\xF3n del cursor.","Los n\xFAmeros de l\xEDnea se muestran cada 10 l\xEDneas.","Controla la visualizaci\xF3n de los n\xFAmeros de l\xEDnea.","N\xFAmero de caracteres monoespaciales en los que se representar\xE1 esta regla del editor.","Color de esta regla del editor.","Muestra reglas verticales despu\xE9s de un cierto n\xFAmero de caracteres monoespaciados. Usa m\xFAltiples valores para mostrar m\xFAltiples reglas. Si la matriz est\xE1 vac\xEDa, no se muestran reglas.","La barra de desplazamiento vertical estar\xE1 visible solo cuando sea necesario.","La barra de desplazamiento vertical estar\xE1 siempre visible.","La barra de desplazamiento vertical estar\xE1 siempre oculta.","Controla la visibilidad de la barra de desplazamiento vertical.","La barra de desplazamiento horizontal estar\xE1 visible solo cuando sea necesario.","La barra de desplazamiento horizontal estar\xE1 siempre visible.","La barra de desplazamiento horizontal estar\xE1 siempre oculta.","Controla la visibilidad de la barra de desplazamiento horizontal.","Ancho de la barra de desplazamiento vertical.","Altura de la barra de desplazamiento horizontal.","Controla si al hacer clic se desplaza por p\xE1gina o salta a la posici\xF3n donde se hace clic.","Cuando se establece, la barra de desplazamiento horizontal no aumentar\xE1 el tama\xF1o del contenido del editor.","Controla si se resaltan todos los caracteres ASCII no b\xE1sicos. Solo los caracteres entre U+0020 y U+007E, tabulaci\xF3n, avance de l\xEDnea y retorno de carro se consideran ASCII b\xE1sicos.","Controla si se resaltan los caracteres que solo reservan espacio o que no tienen ancho.","Controla si se resaltan caracteres que se pueden confundir con caracteres ASCII b\xE1sicos, excepto los que son comunes en la configuraci\xF3n regional del usuario actual.","Controla si los caracteres de los comentarios tambi\xE9n deben estar sujetos al resaltado Unicode.","Controla si los caracteres de las cadenas tambi\xE9n deben estar sujetos al resaltado Unicode.","Define los caracteres permitidos que no se resaltan.","Los caracteres Unicode que son comunes en las configuraciones regionales permitidas no se resaltan.","Controla si se deben mostrar autom\xE1ticamente las sugerencias alineadas en el editor.","Muestra la barra de herramientas de sugerencias insertadas cada vez que se muestra una sugerencia insertada.","Muestra la barra de herramientas de sugerencias insertadas al mantener el puntero sobre una sugerencia insertada.","No mostrar nunca la barra de herramientas de sugerencias insertadas.","Controla cu\xE1ndo mostrar la barra de herramientas de sugerencias insertadas.","Controla c\xF3mo interact\xFAan las sugerencias insertadas con el widget de sugerencias. Si se habilita, el widget de sugerencias no se muestra autom\xE1ticamente cuando hay sugerencias insertadas disponibles.","Controla si est\xE1 habilitada o no la coloraci\xF3n de pares de corchetes. Use {0} para invalidar los colores de resaltado de corchete.","Controla si cada tipo de corchete tiene su propio grupo de colores independiente.","Habilita gu\xEDas de par de corchetes.","Habilita gu\xEDas de par de corchetes solo para el par de corchetes activo.","Deshabilita las gu\xEDas de par de corchetes.","Controla si est\xE1n habilitadas las gu\xEDas de pares de corchetes.","Habilita gu\xEDas horizontales como adici\xF3n a gu\xEDas de par de corchetes verticales.","Habilita gu\xEDas horizontales solo para el par de corchetes activo.","Deshabilita las gu\xEDas de par de corchetes horizontales.","Controla si est\xE1n habilitadas las gu\xEDas de pares de corchetes horizontales.","Controla si el editor debe resaltar el par de corchetes activo.","Controla si el editor debe representar gu\xEDas de sangr\xEDa.","Resalta la gu\xEDa de sangr\xEDa activa.","Resalta la gu\xEDa de sangr\xEDa activa incluso si se resaltan las gu\xEDas de corchetes.","No resalta la gu\xEDa de sangr\xEDa activa.","Controla si el editor debe resaltar la gu\xEDa de sangr\xEDa activa.","Inserte la sugerencia sin sobrescribir el texto a la derecha del cursor.","Inserte la sugerencia y sobrescriba el texto a la derecha del cursor.","Controla si las palabras se sobrescriben al aceptar la finalizaci\xF3n. Tenga en cuenta que esto depende de las extensiones que participan en esta caracter\xEDstica.","Controla si el filtrado y la ordenaci\xF3n de sugerencias se tienen en cuenta para los errores ortogr\xE1ficos peque\xF1os.","Controla si la ordenaci\xF3n mejora las palabras que aparecen cerca del cursor.",'Controla si las selecciones de sugerencias recordadas se comparten entre m\xFAltiples \xE1reas de trabajo y ventanas (necesita "#editor.suggestSelection#").',"Seleccione siempre una sugerencia cuando se desencadene IntelliSense autom\xE1ticamente.","Nunca seleccione una sugerencia cuando desencadene IntelliSense autom\xE1ticamente.","Seleccione una sugerencia solo cuando desencadene IntelliSense desde un car\xE1cter de desencadenador.","Seleccione una sugerencia solo cuando desencadene IntelliSense mientras escribe.","Controla si se selecciona una sugerencia cuando se muestra el widget. Tenga en cuenta que esto solo se aplica a las sugerencias desencadenadas autom\xE1ticamente (`#editor.quickSuggestions#` y `#editor.suggestOnTriggerCharacters#`) y que siempre se selecciona una sugerencia cuando se invoca expl\xEDcitamente, por ejemplo, a trav\xE9s de 'Ctrl+Espacio'.","Controla si un fragmento de c\xF3digo activo impide sugerencias r\xE1pidas.","Controla si mostrar u ocultar iconos en sugerencias.","Controla la visibilidad de la barra de estado en la parte inferior del widget de sugerencias.","Controla si se puede obtener una vista previa del resultado de la sugerencia en el editor.","Controla si los detalles de sugerencia se muestran incorporados con la etiqueta o solo en el widget de detalles.","La configuraci\xF3n est\xE1 en desuso. Ahora puede cambiarse el tama\xF1o del widget de sugerencias.",'Esta configuraci\xF3n est\xE1 en desuso. Use configuraciones separadas como "editor.suggest.showKeyword" o "editor.suggest.showSnippets" en su lugar.','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "method".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de "funci\xF3n".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "constructor".','Cuando se activa IntelliSense muestra sugerencias "obsoletas".','Cuando se activa el filtro IntelliSense se requiere que el primer car\xE1cter coincida con el inicio de una palabra. Por ejemplo, "c" en "Consola" o "WebContext" but _not_ on "descripci\xF3n". Si se desactiva, IntelliSense mostrar\xE1 m\xE1s resultados, pero los ordenar\xE1 seg\xFAn la calidad de la coincidencia.','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "field".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "variable".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "class".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "struct".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "interface".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "module".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "property".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "event".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "operator".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "unit".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de "value".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "constant".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "enum".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "enumMember".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "keyword".','Si est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "text".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de "color".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "file".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "reference".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "customcolor".','Si est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "folder".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "typeParameter".','Cuando est\xE1 habilitado, IntelliSense muestra sugerencias de tipo "snippet".',"Cuando est\xE1 habilitado, IntelliSense muestra sugerencias del usuario.","Cuando est\xE1 habilitado IntelliSense muestra sugerencias para problemas.","Indica si los espacios en blanco iniciales y finales deben seleccionarse siempre.",'Indica si se deben seleccionar las subpalabras (como "foo" en "fooBar" o "foo_bar").',"No hay sangr\xEDa. Las l\xEDneas ajustadas comienzan en la columna 1.","A las l\xEDneas ajustadas se les aplica la misma sangr\xEDa que al elemento primario.","A las l\xEDneas ajustadas se les aplica una sangr\xEDa de +1 respecto al elemento primario.","A las l\xEDneas ajustadas se les aplica una sangr\xEDa de +2 respecto al elemento primario.","Controla la sangr\xEDa de las l\xEDneas ajustadas.",'Controla si puede arrastrar y colocar un archivo en un editor de texto manteniendo presionada la tecla "May\xFAs" (en lugar de abrir el archivo en un editor).',"Controla si se muestra un widget al colocar archivos en el editor. Este widget le permite controlar c\xF3mo se coloca el archivo.","Muestra el widget del selector de colocaci\xF3n despu\xE9s de colocar un archivo en el editor.","No mostrar nunca el widget del selector de colocaci\xF3n. En su lugar, siempre se usa el proveedor de colocaci\xF3n predeterminado.","Controla si se puede pegar contenido de distintas formas.","Controla si se muestra un widget al pegar contenido en el editor. Este widget le permite controlar c\xF3mo se pega el archivo.","Muestra el widget del selector de pegado despu\xE9s de pegar contenido en el editor.","No mostrar nunca el widget del selector de pegado. En su lugar, siempre se usa el comportamiento de pegado predeterminado.",'Controla si se deben aceptar sugerencias en los caracteres de confirmaci\xF3n. Por ejemplo, en Javascript, el punto y coma (";") puede ser un car\xE1cter de confirmaci\xF3n que acepta una sugerencia y escribe ese car\xE1cter.','Aceptar solo una sugerencia con "Entrar" cuando realiza un cambio textual.','Controla si las sugerencias deben aceptarse con "Entrar", adem\xE1s de "TAB". Ayuda a evitar la ambig\xFCedad entre insertar nuevas l\xEDneas o aceptar sugerencias.',"Controla el n\xFAmero de l\xEDneas del editor que pueden ser le\xEDdas por un lector de pantalla a la vez. Cuando detectamos un lector de pantalla, fijamos autom\xE1ticamente el valor por defecto en 500. Advertencia: esto tiene una implicaci\xF3n de rendimiento para n\xFAmeros mayores que el predeterminado.","Contenido del editor","Controlar si un lector de pantalla anuncia sugerencias insertadas.","Utilizar las configuraciones del lenguaje para determinar cu\xE1ndo cerrar los corchetes autom\xE1ticamente.","Cerrar autom\xE1ticamente los corchetes cuando el cursor est\xE9 a la izquierda de un espacio en blanco.","Controla si el editor debe cerrar autom\xE1ticamente los corchetes despu\xE9s de que el usuario agregue un corchete de apertura.","Utilice las configuraciones de idioma para determinar cu\xE1ndo cerrar los comentarios autom\xE1ticamente.","Cerrar autom\xE1ticamente los comentarios solo cuando el cursor est\xE9 a la izquierda de un espacio en blanco.","Controla si el editor debe cerrar autom\xE1ticamente los comentarios despu\xE9s de que el usuario agregue un comentario de apertura.","Quite los corchetes o las comillas de cierre adyacentes solo si se insertaron autom\xE1ticamente.","Controla si el editor debe quitar los corchetes o las comillas de cierre adyacentes al eliminar.","Escriba en las comillas o los corchetes solo si se insertaron autom\xE1ticamente.","Controla si el editor debe escribir entre comillas o corchetes.","Utilizar las configuraciones del lenguaje para determinar cu\xE1ndo cerrar las comillas autom\xE1ticamente. ","Cerrar autom\xE1ticamente las comillas cuando el cursor est\xE9 a la izquierda de un espacio en blanco. ","Controla si el editor debe cerrar autom\xE1ticamente las comillas despu\xE9s de que el usuario agrega uma comilla de apertura.","El editor no insertar\xE1 la sangr\xEDa autom\xE1ticamente.","El editor mantendr\xE1 la sangr\xEDa de la l\xEDnea actual.","El editor respetar\xE1 la sangr\xEDa de la l\xEDnea actual y los corchetes definidos por el idioma.","El editor mantendr\xE1 la sangr\xEDa de la l\xEDnea actual, respetar\xE1 los corchetes definidos por el idioma e invocar\xE1 onEnterRules especiales definidos por idiomas.","El editor respetar\xE1 la sangr\xEDa de la l\xEDnea actual, los corchetes definidos por idiomas y las reglas indentationRules definidas por idiomas, adem\xE1s de invocar reglas onEnterRules especiales.","Controla si el editor debe ajustar autom\xE1ticamente la sangr\xEDa mientras los usuarios escriben, pegan, mueven o sangran l\xEDneas.","Use las configuraciones de idioma para determinar cu\xE1ndo delimitar las selecciones autom\xE1ticamente.","Envolver con comillas, pero no con corchetes.","Envolver con corchetes, pero no con comillas.","Controla si el editor debe rodear autom\xE1ticamente las selecciones al escribir comillas o corchetes.","Emula el comportamiento de selecci\xF3n de los caracteres de tabulaci\xF3n al usar espacios para la sangr\xEDa. La selecci\xF3n se aplicar\xE1 a las tabulaciones.","Controla si el editor muestra CodeLens.","Controla la familia de fuentes para CodeLens.",'Controla el tama\xF1o de fuente de CodeLens en p\xEDxeles. Cuando se establece en 0, se usa el 90\xA0% de "#editor.fontSize#".',"Controla si el editor debe representar el Selector de colores y los elementos Decorator de color en l\xEDnea.","Hacer que el selector de colores aparezca tanto al hacer clic como al mantener el puntero sobre el decorador de color","Hacer que el selector de colores aparezca al pasar el puntero sobre el decorador de color","Hacer que el selector de colores aparezca al hacer clic en el decorador de color","Controla la condici\xF3n para que un selector de colores aparezca de un decorador de color","Controla el n\xFAmero m\xE1ximo de decoradores de color que se pueden representar en un editor a la vez.","Habilite que la selecci\xF3n con el mouse y las teclas est\xE9 realizando la selecci\xF3n de columnas.","Controla si el resaltado de sintaxis debe ser copiado al portapapeles.","Controla el estilo de animaci\xF3n del cursor.","La animaci\xF3n del s\xEDmbolo de intercalaci\xF3n suave est\xE1 deshabilitada.","La animaci\xF3n de s\xEDmbolo de intercalaci\xF3n suave solo se habilita cuando el usuario mueve el cursor con un gesto expl\xEDcito.","La animaci\xF3n de s\xEDmbolo de intercalaci\xF3n suave siempre est\xE1 habilitada.","Controla si la animaci\xF3n suave del cursor debe estar habilitada.","Controla el estilo del cursor.",'Controla el n\xFAmero m\xEDnimo de l\xEDneas iniciales visibles (m\xEDnimo 0) y l\xEDneas finales (m\xEDnimo 1) que rodean el cursor. Se conoce como "scrollOff" o "scrollOffset" en otros editores.','Solo se aplica "cursorSurroundingLines" cuando se desencadena mediante el teclado o la API.','"cursorSurroundingLines" se aplica siempre.','Controla cuando se debe aplicar "#cursorSurroundingLines#".','Controla el ancho del cursor cuando "#editor.cursorStyle#" se establece en "line".',"Controla si el editor debe permitir mover las selecciones mediante arrastrar y colocar.","Use un nuevo m\xE9todo de representaci\xF3n con svgs.","Use un nuevo m\xE9todo de representaci\xF3n con caracteres de fuente.","Use el m\xE9todo de representaci\xF3n estable.","Controla si los espacios en blanco se representan con un nuevo m\xE9todo experimental.",'Multiplicador de la velocidad de desplazamiento al presionar "Alt".',"Controla si el editor tiene el plegado de c\xF3digo habilitado.","Utilice una estrategia de plegado espec\xEDfica del idioma, si est\xE1 disponible, de lo contrario la basada en sangr\xEDa.","Utilice la estrategia de plegado basada en sangr\xEDa.","Controla la estrategia para calcular rangos de plegado.","Controla si el editor debe destacar los rangos plegados.","Permite controlar si el editor contrae autom\xE1ticamente los rangos de importaci\xF3n.","N\xFAmero m\xE1ximo de regiones plegables. Si aumenta este valor, es posible que el editor tenga menos capacidad de respuesta cuando el origen actual tiene un gran n\xFAmero de regiones plegables.","Controla si al hacer clic en el contenido vac\xEDo despu\xE9s de una l\xEDnea plegada se desplegar\xE1 la l\xEDnea.","Controla la familia de fuentes.","Controla si el editor debe dar formato autom\xE1ticamente al contenido pegado. Debe haber disponible un formateador capaz de aplicar formato a un rango dentro de un documento. ","Controla si el editor debe dar formato a la l\xEDnea autom\xE1ticamente despu\xE9s de escribirla.","Controla si el editor debe representar el margen de glifo vertical. El margen de glifo se usa, principalmente, para depuraci\xF3n.","Controla si el cursor debe ocultarse en la regla de informaci\xF3n general.","Controla el espacio entre letras en p\xEDxeles.","Controla si el editor tiene habilitada la edici\xF3n vinculada. Dependiendo del lenguaje, los s\xEDmbolos relacionados (por ejemplo, las etiquetas HTML) se actualizan durante la edici\xF3n.","Controla si el editor debe detectar v\xEDnculos y hacerlos interactivos.","Resaltar par\xE9ntesis coincidentes.",'Se usar\xE1 un multiplicador en los eventos de desplazamiento de la rueda del mouse "deltaX" y "deltaY". ','Ampliar la fuente del editor cuando se use la rueda del mouse mientras se presiona "Ctrl".',"Combinar varios cursores cuando se solapan.",'Se asigna a "Control" en Windows y Linux y a "Comando" en macOS.','Se asigna a "Alt" en Windows y Linux y a "Opci\xF3n" en macOS.',"El modificador que se usar\xE1 para agregar varios cursores con el mouse. Los gestos del mouse Ir a definici\xF3n y Abrir v\xEDnculo se adaptar\xE1n de modo que no entren en conflicto con el [modificador multicursor](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).","Cada cursor pega una \xFAnica l\xEDnea del texto.","Cada cursor pega el texto completo.","Controla el pegado cuando el recuento de l\xEDneas del texto pegado coincide con el recuento de cursores.","Controla el n\xFAmero m\xE1ximo de cursores que puede haber en un editor activo a la vez.","No resalta las repeticiones.","Resalta las repeticiones solo en el archivo actual.","Experimental: Resalta las repeticiones en todos los archivos abiertos v\xE1lidos.","Controla si las repeticiones deben resaltarse en los archivos abiertos.","Controla si debe dibujarse un borde alrededor de la regla de informaci\xF3n general.","Enfocar el \xE1rbol al abrir la inspecci\xF3n","Enfocar el editor al abrir la inspecci\xF3n","Controla si se debe enfocar el editor en l\xEDnea o el \xE1rbol en el widget de vista.","Controla si el gesto del mouse Ir a definici\xF3n siempre abre el widget interactivo.","Controla el retraso, en milisegundos, tras el cual aparecer\xE1n sugerencias r\xE1pidas.","Controla si el editor cambia el nombre autom\xE1ticamente en el tipo.",'En desuso. Utilice "editor.linkedEditing" en su lugar.',"Controla si el editor debe representar caracteres de control.","Representar el n\xFAmero de la \xFAltima l\xEDnea cuando el archivo termina con un salto de l\xEDnea.","Resalta el medianil y la l\xEDnea actual.","Controla c\xF3mo debe representar el editor el resaltado de l\xEDnea actual.","Controla si el editor debe representar el resaltado de la l\xEDnea actual solo cuando el editor est\xE1 enfocado.","Representa caracteres de espacio en blanco, excepto los espacios individuales entre palabras.","Represente los caracteres de espacio en blanco solo en el texto seleccionado.","Representa solo los caracteres de espacio en blanco al final.","Controla la forma en que el editor debe representar los caracteres de espacio en blanco.","Controla si las selecciones deber\xEDan tener las esquinas redondeadas.","Controla el n\xFAmero de caracteres adicionales a partir del cual el editor se desplazar\xE1 horizontalmente.","Controla si el editor seguir\xE1 haciendo scroll despu\xE9s de la \xFAltima l\xEDnea.","Despl\xE1cese solo a lo largo del eje predominante cuando se desplace vertical y horizontalmente al mismo tiempo. Evita la deriva horizontal cuando se desplaza verticalmente en un trackpad.","Controla si el portapapeles principal de Linux debe admitirse.","Controla si el editor debe destacar las coincidencias similares a la selecci\xF3n.","Mostrar siempre los controles de plegado.","No mostrar nunca los controles de plegado y reducir el tama\xF1o del medianil.","Mostrar solo los controles de plegado cuando el mouse est\xE1 sobre el medianil.","Controla cu\xE1ndo se muestran los controles de plegado en el medianil.","Controla el fundido de salida del c\xF3digo no usado.","Controla las variables en desuso tachadas.","Mostrar sugerencias de fragmentos de c\xF3digo por encima de otras sugerencias.","Mostrar sugerencias de fragmentos de c\xF3digo por debajo de otras sugerencias.","Mostrar sugerencias de fragmentos de c\xF3digo con otras sugerencias.","No mostrar sugerencias de fragmentos de c\xF3digo.","Controla si se muestran los fragmentos de c\xF3digo con otras sugerencias y c\xF3mo se ordenan.","Controla si el editor se desplazar\xE1 con una animaci\xF3n.","Controla si se debe proporcionar la sugerencia de accesibilidad a los usuarios del lector de pantalla cuando se muestra una finalizaci\xF3n insertada.","Tama\xF1o de fuente del widget de sugerencias. Cuando se establece en {0}, se usa el valor de {1}.","Alto de l\xEDnea para el widget de sugerencias. Cuando se establece en {0}, se usa el valor de {1}. El valor m\xEDnimo es 8.","Controla si deben aparecer sugerencias de forma autom\xE1tica al escribir caracteres desencadenadores.","Seleccionar siempre la primera sugerencia.",'Seleccione sugerencias recientes a menos que al escribir m\xE1s se seleccione una, por ejemplo, "console.| -> console.log" porque "log" se ha completado recientemente.','Seleccione sugerencias basadas en prefijos anteriores que han completado esas sugerencias, por ejemplo, "co -> console" y "con -> const".',"Controla c\xF3mo se preseleccionan las sugerencias cuando se muestra la lista,","La pesta\xF1a se completar\xE1 insertando la mejor sugerencia de coincidencia encontrada al presionar la pesta\xF1a","Deshabilitar los complementos para pesta\xF1as.","La pesta\xF1a se completa con fragmentos de c\xF3digo cuando su prefijo coincide. Funciona mejor cuando las 'quickSuggestions' no est\xE1n habilitadas.","Habilita completar pesta\xF1as.","Los terminadores de l\xEDnea no habituales se quitan autom\xE1ticamente.","Los terminadores de l\xEDnea no habituales se omiten.","Advertencia de terminadores de l\xEDnea inusuales que se quitar\xE1n.","Quite los terminadores de l\xEDnea inusuales que podr\xEDan provocar problemas.","La inserci\xF3n y eliminaci\xF3n del espacio en blanco sigue a las tabulaciones.","Use la regla de salto de l\xEDnea predeterminada.","Los saltos de palabra no deben usarse para texto chino, japon\xE9s o coreano (CJK). El comportamiento del texto distinto a CJK es el mismo que el normal.","Controla las reglas de salto de palabra usadas para texto chino, japon\xE9s o coreano (CJK).","Caracteres que se usar\xE1n como separadores de palabras al realizar operaciones o navegaciones relacionadas con palabras.","Las l\xEDneas no se ajustar\xE1n nunca.","Las l\xEDneas se ajustar\xE1n en el ancho de la ventanilla.",'Las l\xEDneas se ajustar\xE1n al valor de "#editor.wordWrapColumn#". ','Las l\xEDneas se ajustar\xE1n al valor que sea inferior: el tama\xF1o de la ventanilla o el valor de "#editor.wordWrapColumn#".',"Controla c\xF3mo deben ajustarse las l\xEDneas.",'Controla la columna de ajuste del editor cuando "#editor.wordWrap#" es "wordWrapColumn" o "bounded".',"Controla si las decoraciones de color en l\xEDnea deben mostrarse con el proveedor de colores del documento predeterminado.","Controla si el editor recibe las pesta\xF1as o las aplaza al \xE1rea de trabajo para la navegaci\xF3n."],"vs/editor/common/core/editorColorRegistry":["Color de fondo para la l\xEDnea resaltada en la posici\xF3n del cursor.","Color de fondo del borde alrededor de la l\xEDnea en la posici\xF3n del cursor.","Color de fondo de rangos resaltados, como en abrir r\xE1pido y encontrar caracter\xEDsticas. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de fondo del borde alrededor de los intervalos resaltados.","Color de fondo del s\xEDmbolo destacado, como Ir a definici\xF3n o Ir al siguiente/anterior s\xEDmbolo. El color no debe ser opaco para no ocultar la decoraci\xF3n subyacente.","Color de fondo del borde alrededor de los s\xEDmbolos resaltados.","Color del cursor del editor.","Color de fondo del cursor de edici\xF3n. Permite personalizar el color del caracter solapado por el bloque del cursor.","Color de los caracteres de espacio en blanco del editor.","Color de n\xFAmeros de l\xEDnea del editor.","Color de las gu\xEDas de sangr\xEDa del editor.",'"editorIndentGuide.background" est\xE1 en desuso. Use "editorIndentGuide.background1" en su lugar.',"Color de las gu\xEDas de sangr\xEDa activas del editor.",'"editorIndentGuide.activeBackground" est\xE1 en desuso. Use "editorIndentGuide.activeBackground1" en su lugar.',"Color de las gu\xEDas de sangr\xEDa del editor (1).","Color de las gu\xEDas de sangr\xEDa del editor (2).","Color de las gu\xEDas de sangr\xEDa del editor (3).","Color de las gu\xEDas de sangr\xEDa del editor (4).","Color de las gu\xEDas de sangr\xEDa del editor (5).","Color de las gu\xEDas de sangr\xEDa del editor (6).","Color de las gu\xEDas de sangr\xEDa del editor activo (1).","Color de las gu\xEDas de sangr\xEDa del editor activo (2).","Color de las gu\xEDas de sangr\xEDa del editor activo (3).","Color de las gu\xEDas de sangr\xEDa del editor activo (4).","Color de las gu\xEDas de sangr\xEDa del editor activo (5).","Color de las gu\xEDas de sangr\xEDa del editor activo (6).","Color del n\xFAmero de l\xEDnea activa en el editor","ID es obsoleto. Usar en lugar 'editorLineNumber.activeForeground'. ","Color del n\xFAmero de l\xEDnea activa en el editor","Color de la l\xEDnea final del editor cuando editor.renderFinalNewline se establece en atenuado.","Color de las reglas del editor","Color principal de lentes de c\xF3digo en el editor","Color de fondo tras corchetes coincidentes","Color de bloques con corchetes coincidentes","Color del borde de la regla de visi\xF3n general.","Color de fondo de la regla de informaci\xF3n general del editor.","Color de fondo del margen del editor. Este espacio contiene los m\xE1rgenes de glifos y los n\xFAmeros de l\xEDnea.","Color del borde de c\xF3digo fuente innecesario (sin usar) en el editor.",`Opacidad de c\xF3digo fuente innecesario (sin usar) en el editor. Por ejemplo, "#000000c0" representar\xE1 el c\xF3digo con un 75 % de opacidad. Para temas de alto contraste, utilice el color del tema 'editorUnnecessaryCode.border' para resaltar el c\xF3digo innecesario en vez de atenuarlo.`,"Color del borde del texto fantasma en el editor.","Color de primer plano del texto fantasma en el editor.","Color de fondo del texto fantasma en el editor.","Color de marcador de regla general para los destacados de rango. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de marcador de regla de informaci\xF3n general para errores. ","Color de marcador de regla de informaci\xF3n general para advertencias.","Color de marcador de regla de informaci\xF3n general para mensajes informativos. ","Color de primer plano de los corchetes (1). Requiere que se habilite la coloraci\xF3n del par de corchetes.","Color de primer plano de los corchetes (2). Requiere que se habilite la coloraci\xF3n del par de corchetes.","Color de primer plano de los corchetes (3). Requiere que se habilite la coloraci\xF3n del par de corchetes.","Color de primer plano de los corchetes (4). Requiere que se habilite la coloraci\xF3n del par de corchetes.","Color de primer plano de los corchetes (5). Requiere que se habilite la coloraci\xF3n del par de corchetes.","Color de primer plano de los corchetes (6). Requiere que se habilite la coloraci\xF3n del par de corchetes.","Color de primer plano de corchetes inesperados.","Color de fondo de las gu\xEDas de par de corchetes inactivos (1). Requiere habilitar gu\xEDas de par de corchetes.","Color de fondo de las gu\xEDas de par de corchetes inactivos (2). Requiere habilitar gu\xEDas de par de corchetes.","Color de fondo de las gu\xEDas de par de corchetes inactivos (3). Requiere habilitar gu\xEDas de par de corchetes.","Color de fondo de las gu\xEDas de par de corchetes inactivos (4). Requiere habilitar gu\xEDas de par de corchetes.","Color de fondo de las gu\xEDas de par de corchetes inactivos (5). Requiere habilitar gu\xEDas de par de corchetes.","Color de fondo de las gu\xEDas de par de corchetes inactivos (6). Requiere habilitar gu\xEDas de par de corchetes.","Color de fondo de las gu\xEDas de pares de corchetes activos (1). Requiere habilitar gu\xEDas de par de corchetes.","Color de fondo de las gu\xEDas de par de corchetes activos (2). Requiere habilitar gu\xEDas de par de corchetes.","Color de fondo de las gu\xEDas de pares de corchetes activos (3). Requiere habilitar gu\xEDas de par de corchetes.","Color de fondo de las gu\xEDas de par de corchetes activos (4). Requiere habilitar gu\xEDas de par de corchetes.","Color de fondo de las gu\xEDas de par de corchetes activos (5). Requiere habilitar gu\xEDas de par de corchetes.","Color de fondo de las gu\xEDas de par de corchetes activos (6). Requiere habilitar gu\xEDas de par de corchetes.","Color de borde usado para resaltar caracteres Unicode.","Color de borde usado para resaltar caracteres unicode."],"vs/editor/common/editorContextKeys":["Si el texto del editor tiene el foco (el cursor parpadea)","Si el editor o un widget del editor tiene el foco (por ejemplo, el foco est\xE1 en el widget de b\xFAsqueda)","Si un editor o una entrada de texto enriquecido tienen el foco (el cursor parpadea)","Si el editor es de solo lectura","Si el contexto es un editor de diferencias","Si el contexto es un editor de diferencias incrustado","Si el contexto es un editor de diferencias m\xFAltiples","Si todos los archivos del editor de diferencias m\xFAltiples est\xE1n contra\xEDdos","Si el editor de diferencias tiene cambios","Indica si se selecciona un bloque de c\xF3digo movido para la comparaci\xF3n","Si el visor de diferencias accesible est\xE1 visible","Indica si se alcanza el punto de interrupci\xF3n insertado en paralelo del editor de diferencias",'Si "editor.columnSelection" se ha habilitado',"Si el editor tiene texto seleccionado","Si el editor tiene varias selecciones",'Si "Tabulaci\xF3n" mover\xE1 el foco fuera del editor',"Si el mantenimiento del puntero del editor es visible","Si se centra el desplazamiento del editor","Si el desplazamiento permanente est\xE1 centrado","Si el desplazamiento permanente est\xE1 visible","Si el selector de colores independiente est\xE1 visible","Si el selector de colores independiente est\xE1 centrado","Si el editor forma parte de otro m\xE1s grande (por ejemplo, blocs de notas)","Identificador de idioma del editor","Si el editor tiene un proveedor de elementos de finalizaci\xF3n","Si el editor tiene un proveedor de acciones de c\xF3digo","Si el editor tiene un proveedor de CodeLens","Si el editor tiene un proveedor de definiciones","Si el editor tiene un proveedor de declaraciones","Si el editor tiene un proveedor de implementaci\xF3n","Si el editor tiene un proveedor de definiciones de tipo","Si el editor tiene un proveedor de contenido con mantenimiento del puntero","Si el editor tiene un proveedor de resaltado de documentos","Si el editor tiene un proveedor de s\xEDmbolos de documentos","Si el editor tiene un proveedor de referencia","Si el editor tiene un proveedor de cambio de nombre","Si el editor tiene un proveedor de ayuda de signatura","Si el editor tiene un proveedor de sugerencias insertadas","Si el editor tiene un proveedor de formatos de documento","Si el editor tiene un proveedor de formatos de selecci\xF3n de documentos","Si el editor tiene varios proveedores de formatos del documento","Si el editor tiene varios proveedores de formato de la selecci\xF3n de documentos"],"vs/editor/common/languages":["matriz","booleano","clase","constante","constructor","enumeraci\xF3n","miembro de la enumeraci\xF3n","evento","campo","archivo","funci\xF3n","interfaz","clave","m\xE9todo","m\xF3dulo","espacio de nombres","NULL","n\xFAmero","objeto","operador","paquete","propiedad","cadena","estructura","par\xE1metro de tipo","variable","{0} ({1})"],"vs/editor/common/languages/modesRegistry":["Texto sin formato"],"vs/editor/common/model/editStack":["Escribiendo"],"vs/editor/common/standaloneStrings":["Desarrollador: inspeccionar tokens","Vaya a L\xEDnea/Columna...","Mostrar todos los proveedores de acceso r\xE1pido","Paleta de comandos","Mostrar y ejecutar comandos","Ir a s\xEDmbolo...","Ir a s\xEDmbolo por categor\xEDa...","Contenido del editor","Presione Alt+F1 para ver las opciones de accesibilidad.","Alternar tema de contraste alto","{0} ediciones realizadas en {1} archivos"],"vs/editor/common/viewLayout/viewLineRenderer":["Mostrar m\xE1s ({0})","{0} caracteres"],"vs/editor/contrib/anchorSelect/browser/anchorSelect":["Delimitador de la selecci\xF3n","Delimitador establecido en {0}:{1}","Establecer el delimitador de la selecci\xF3n","Ir al delimitador de la selecci\xF3n","Seleccionar desde el delimitador hasta el cursor","Cancelar el delimitador de la selecci\xF3n"],"vs/editor/contrib/bracketMatching/browser/bracketMatching":["Resumen color de marcador de regla para corchetes.","Ir al corchete","Seleccionar para corchete","Quitar corchetes","Ir al &&corchete","Se selecciona el texto que est\xE1 dentro, incluyendo los corchetes o las llaves"],"vs/editor/contrib/caretOperations/browser/caretOperations":["Mover el texto seleccionado a la izquierda","Mover el texto seleccionado a la derecha"],"vs/editor/contrib/caretOperations/browser/transpose":["Transponer letras"],"vs/editor/contrib/clipboard/browser/clipboard":["Cor&&tar","Cortar","Cortar","Cortar","&&Copiar","Copiar","Copiar","Copiar","Copiar como","Copiar como","Compartir","Compartir","Compartir","&&Pegar","Pegar","Pegar","Pegar","Copiar con resaltado de sintaxis"],"vs/editor/contrib/codeAction/browser/codeAction":["Se ha producido un error desconocido al aplicar la acci\xF3n de c\xF3digo"],"vs/editor/contrib/codeAction/browser/codeActionCommands":["Tipo de la acci\xF3n de c\xF3digo que se va a ejecutar.","Controla cu\xE1ndo se aplican las acciones devueltas.","Aplicar siempre la primera acci\xF3n de c\xF3digo devuelto.","Aplicar la primera acci\xF3n de c\xF3digo devuelta si solo hay una.","No aplique las acciones de c\xF3digo devuelto.","Controla si solo se deben devolver las acciones de c\xF3digo preferidas.","Correcci\xF3n R\xE1pida","No hay acciones de c\xF3digo disponibles",'No hay acciones de c\xF3digo preferidas para "{0}" disponibles','No hay ninguna acci\xF3n de c\xF3digo para "{0}" disponible.',"No hay acciones de c\xF3digo preferidas disponibles","No hay acciones de c\xF3digo disponibles","Refactorizar...",'No hay refactorizaciones preferidas de "{0}" disponibles','No hay refactorizaciones de "{0}" disponibles',"No hay ninguna refactorizaci\xF3n favorita disponible.","No hay refactorizaciones disponibles","Acci\xF3n de c\xF3digo fuente...",'No hay acciones de origen preferidas para "{0}" disponibles','No hay ninguna acci\xF3n de c\xF3digo fuente para "{0}" disponible.',"No hay ninguna acci\xF3n de c\xF3digo fuente favorita disponible.","No hay acciones de origen disponibles","Organizar Importaciones","No hay acciones de importaci\xF3n disponibles","Corregir todo","No est\xE1 disponible la acci\xF3n de corregir todo","Corregir autom\xE1ticamente...","No hay autocorrecciones disponibles"],"vs/editor/contrib/codeAction/browser/codeActionContributions":["Activar/desactivar la visualizaci\xF3n de los encabezados de los grupos en el men\xFA de Acci\xF3n de c\xF3digo.","Habilita o deshabilita la visualizaci\xF3n de la correcci\xF3n r\xE1pida m\xE1s cercana dentro de una l\xEDnea cuando no est\xE1 actualmente en un diagn\xF3stico."],"vs/editor/contrib/codeAction/browser/codeActionController":["Contexto: {0} en la l\xEDnea {1} y columna {2}.","Ocultar deshabilitado","Mostrar elementos deshabilitados"],"vs/editor/contrib/codeAction/browser/codeActionMenu":["M\xE1s Acciones...","Correcci\xF3n r\xE1pida","Extraer","Insertado","Reescribir","Mover","Delimitar con","Acci\xF3n de origen"],"vs/editor/contrib/codeAction/browser/lightBulbWidget":["Mostrar acciones de c\xF3digo. Correcci\xF3n r\xE1pida preferida disponible ({0})","Mostrar acciones de c\xF3digo ({0})","Mostrar acciones de c\xF3digo","Iniciar chat en l\xEDnea ({0})","Iniciar chat en l\xEDnea","Desencadenar acci\xF3n de IA"],"vs/editor/contrib/codelens/browser/codelensController":["Mostrar comandos de lente de c\xF3digo para la l\xEDnea actual","Seleccionar un comando"],"vs/editor/contrib/colorPicker/browser/colorPickerWidget":["Haga clic para alternar las opciones de color (rgb/hsl/hex)","Icono para cerrar el selector de colores"],"vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions":["Mostrar o centrar Selector de colores independientes","&Mostrar o centrar Selector de colores independientes","Ocultar la Selector de colores","Insertar color con Selector de colores independiente"],"vs/editor/contrib/comment/browser/comment":["Alternar comentario de l\xEDnea","&&Alternar comentario de l\xEDnea","Agregar comentario de l\xEDnea","Quitar comentario de l\xEDnea","Alternar comentario de bloque","Alternar &&bloque de comentario"],"vs/editor/contrib/contextmenu/browser/contextmenu":["Minimapa","Representar caracteres","Tama\xF1o vertical","Proporcional","Relleno","Ajustar","Control deslizante","Pasar el mouse","Siempre","Mostrar men\xFA contextual del editor"],"vs/editor/contrib/cursorUndo/browser/cursorUndo":["Cursor Deshacer","Cursor Rehacer"],"vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution":["Pegar como...","Id. de la edici\xF3n pegada que se intenta aplicar. Si no se proporciona, el editor mostrar\xE1 un selector."],"vs/editor/contrib/dropOrPasteInto/browser/copyPasteController":["Si se muestra el widget de pegado","Mostrar opciones de pegado...","Ejecutando controladores de pegado. Haga clic para cancelar.","Seleccionar acci\xF3n pegar","Ejecutando controladores de pegado"],"vs/editor/contrib/dropOrPasteInto/browser/defaultProviders":["Integrado","Insertar texto sin formato","Insertar URIs","Insertar URI","Insertar rutas de acceso","Insertar ruta de acceso","Insertar rutas de acceso relativas","Insertar ruta de acceso relativa"],"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution":["Configura el proveedor de colocaci\xF3n predeterminado que se usar\xE1 para el contenido de un tipo MIME determinado."],"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController":["Si se muestra el widget de colocaci\xF3n","Mostrar opciones de colocaci\xF3n...","Ejecutando controladores de colocaci\xF3n. Haga clic para cancelar."],"vs/editor/contrib/editorState/browser/keybindingCancellation":['Indica si el editor ejecuta una operaci\xF3n que se puede cancelar como, por ejemplo, "Inspeccionar referencias"'],"vs/editor/contrib/find/browser/findController":["El archivo es demasiado grande para realizar una operaci\xF3n de reemplazar todo.","Buscar","&&Buscar",`Invalida la marca "Usar expresi\xF3n regular".\r
La marca no se guardar\xE1 para el futuro.\r
0: No hacer nada\r
1: True\r
2: False`,`Invalida la marca "Hacer coincidir palabra completa\u201D.\r
La marca no se guardar\xE1 para el futuro.\r
0: No hacer nada\r
1: True\r
2: False`,`Invalida la marca "Caso matem\xE1tico".\r
La marca no se guardar\xE1 para el futuro.\r
0: No hacer nada\r
1: True\r
2: False`,`Invalida la marca "Conservar may\xFAsculas y min\xFAsculas.\r
La marca no se guardar\xE1 para el futuro.\r
0: No hacer nada\r
1: True\r
2: False`,"B\xFAsqueda con argumentos","Buscar con selecci\xF3n","Buscar siguiente","Buscar anterior","Ir a Coincidencia...","No hay coincidencias. Intente buscar otra cosa.","Escriba un n\xFAmero para ir a una coincidencia espec\xEDfica (entre 1 y {0})","Escriba un n\xFAmero entre 1 y {0}","Escriba un n\xFAmero entre 1 y {0}","Buscar selecci\xF3n siguiente","Buscar selecci\xF3n anterior","Reemplazar","&&Reemplazar"],"vs/editor/contrib/find/browser/findWidget":['Icono para "Buscar en selecci\xF3n" en el widget de b\xFAsqueda del editor.',"Icono para indicar que el widget de b\xFAsqueda del editor est\xE1 contra\xEDdo.","Icono para indicar que el widget de b\xFAsqueda del editor est\xE1 expandido.",'Icono para "Reemplazar" en el widget de b\xFAsqueda del editor.','Icono para "Reemplazar todo" en el widget de b\xFAsqueda del editor.','Icono para "Buscar anterior" en el widget de b\xFAsqueda del editor.','Icono para "Buscar siguiente" en el widget de b\xFAsqueda del editor.',"Buscar y reemplazar","Buscar","Buscar","Coincidencia anterior","Coincidencia siguiente","Buscar en selecci\xF3n","Cerrar","Reemplazar","Reemplazar","Reemplazar","Reemplazar todo","Alternar reemplazar","S\xF3lo los primeros {0} resultados son resaltados, pero todas las operaciones de b\xFAsqueda trabajan en todo el texto.","{0} de {1}","No hay resultados","Encontrados: {0}",'{0} encontrado para "{1}"','{0} encontrado para "{1}", en {2}','{0} encontrado para "{1}"',"Ctrl+Entrar ahora inserta un salto de l\xEDnea en lugar de reemplazar todo. Puede modificar el enlace de claves para editor.action.replaceAll para invalidar este comportamiento."],"vs/editor/contrib/folding/browser/folding":["Desplegar","Desplegar de forma recursiva","Plegar","Alternar plegado","Plegar de forma recursiva","Cerrar todos los comentarios de bloque","Plegar todas las regiones","Desplegar Todas las Regiones","Plegar todas excepto las seleccionadas","Desplegar todas excepto las seleccionadas","Plegar todo","Desplegar todo","Ir al plegado primario","Ir al rango de plegado anterior","Ir al rango de plegado siguiente","Crear rango de plegado a partir de la selecci\xF3n","Quitar rangos de plegado manuales","Nivel de plegamiento {0}"],"vs/editor/contrib/folding/browser/foldingDecorations":["Color de fondo detr\xE1s de los rangos plegados. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color del control plegable en el medianil del editor.","Icono de rangos expandidos en el margen de glifo del editor.","Icono de rangos contra\xEDdos en el margen de glifo del editor.","Icono de intervalos contra\xEDdos manualmente en el margen del glifo del editor.","Icono de intervalos expandidos manualmente en el margen del glifo del editor."],"vs/editor/contrib/fontZoom/browser/fontZoom":["Acercarse a la tipograf\xEDa del editor","Alejarse de la tipograf\xEDa del editor","Restablecer alejamiento de la tipograf\xEDa del editor"],"vs/editor/contrib/format/browser/formatActions":["Dar formato al documento","Dar formato a la selecci\xF3n"],"vs/editor/contrib/gotoError/browser/gotoError":["Ir al siguiente problema (Error, Advertencia, Informaci\xF3n)","Icono para ir al marcador siguiente.","Ir al problema anterior (Error, Advertencia, Informaci\xF3n)","Icono para ir al marcador anterior.","Ir al siguiente problema en Archivos (Error, Advertencia, Informaci\xF3n)","Siguiente &&problema","Ir al problema anterior en Archivos (Error, Advertencia, Informaci\xF3n)","Anterior &&problema"],"vs/editor/contrib/gotoError/browser/gotoErrorWidget":["Error","Advertencia","Informaci\xF3n","Sugerencia","{0} en {1}. ","{0} de {1} problemas","{0} de {1} problema","Color de los errores del widget de navegaci\xF3n de marcadores del editor.","Fondo del encabezado del error del widget de navegaci\xF3n del marcador de editor.","Color de las advertencias del widget de navegaci\xF3n de marcadores del editor.","Fondo del encabezado de la advertencia del widget de navegaci\xF3n del marcador de editor.","Color del widget informativo marcador de navegaci\xF3n en el editor.","Fondo del encabezado de informaci\xF3n del widget de navegaci\xF3n del marcador de editor.","Fondo del widget de navegaci\xF3n de marcadores del editor."],"vs/editor/contrib/gotoSymbol/browser/goToCommands":["Ver","Definiciones",'No se encontr\xF3 ninguna definici\xF3n para "{0}"',"No se encontr\xF3 ninguna definici\xF3n","Ir a definici\xF3n","Ir a &&definici\xF3n","Abrir definici\xF3n en el lateral","Ver la definici\xF3n sin salir","Declaraciones","No se encontr\xF3 ninguna definici\xF3n para '{0}'","No se encontr\xF3 ninguna declaraci\xF3n","Ir a Definici\xF3n","Ir a &&declaraci\xF3n","No se encontr\xF3 ninguna definici\xF3n para '{0}'","No se encontr\xF3 ninguna declaraci\xF3n","Inspeccionar Definici\xF3n","Definiciones de tipo",'No se encontr\xF3 ninguna definici\xF3n de tipo para "{0}"',"No se encontr\xF3 ninguna definici\xF3n de tipo","Ir a la definici\xF3n de tipo","Ir a la definici\xF3n de &&tipo","Inspeccionar definici\xF3n de tipo","Implementaciones",'No se encontr\xF3 ninguna implementaci\xF3n para "{0}"',"No se encontr\xF3 ninguna implementaci\xF3n","Ir a Implementaciones","Ir a &&implementaciones","Inspeccionar implementaciones",'No se ha encontrado ninguna referencia para "{0}".',"No se encontraron referencias","Ir a Referencias","Ir a &&referencias","Referencias","Inspeccionar Referencias","Referencias","Ir a cualquier s\xEDmbolo","Ubicaciones",'No hay resultados para "{0}"',"Referencias"],"vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition":["Haga clic para mostrar {0} definiciones."],"vs/editor/contrib/gotoSymbol/browser/peek/referencesController":['Indica si est\xE1 visible la inspecci\xF3n de referencias, como "Inspecci\xF3n de referencias" o "Ver la definici\xF3n sin salir".',"Cargando...","{0} ({1})"],"vs/editor/contrib/gotoSymbol/browser/peek/referencesTree":["{0} referencias","{0} referencia","Referencias"],"vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget":["vista previa no disponible","No hay resultados","Referencias"],"vs/editor/contrib/gotoSymbol/browser/referencesModel":["en {0} en la l\xEDnea {1} en la columna {2}","{0} en {1} en la l\xEDnea {2} en la columna {3}","1 s\xEDmbolo en {0}, ruta de acceso completa {1}","{0} s\xEDmbolos en {1}, ruta de acceso completa {2}","No se encontraron resultados","Encontr\xF3 1 s\xEDmbolo en {0}","Encontr\xF3 {0} s\xEDmbolos en {1}","Encontr\xF3 {0} s\xEDmbolos en {1} archivos"],"vs/editor/contrib/gotoSymbol/browser/symbolNavigation":["Indica si hay ubicaciones de s\xEDmbolos a las que se pueda navegar solo con el teclado.","S\xEDmbolo {0} de {1}, {2} para el siguiente","S\xEDmbolo {0} de {1}"],"vs/editor/contrib/hover/browser/hover":["Mostrar o centrarse al mantener el puntero","El cuadro del elemento sobre el que se ha pasado el rat\xF3n se enfocar\xE1 autom\xE1ticamente.","El cuadro del elemento sobre el que se ha pasado el rat\xF3n se enfocar\xE1 solo si ya est\xE1 visible.","Se enfocar\xE1 el cuadro que aparece cuando se pasa el rat\xF3n por encima de un elemento.","Mostrar vista previa de la definici\xF3n que aparece al mover el puntero","Desplazar hacia arriba al mantener el puntero","Desplazar hacia abajo al mantener el puntero","Desplazar al mantener el puntero a la izquierda","Desplazar al mantener el puntero a la derecha","Desplazamiento de p\xE1gina hacia arriba","Desplazamiento de p\xE1gina hacia abajo","Ir al puntero superior","Ir a la parte inferior al mantener el puntero"],"vs/editor/contrib/hover/browser/markdownHoverParticipant":["Cargando...",'Representaci\xF3n en pausa durante una l\xEDnea larga por motivos de rendimiento. Esto se puede configurar mediante "editor.stopRenderingLineAfter".','Por motivos de rendimiento, la tokenizaci\xF3n se omite con filas largas. Esta opci\xF3n se puede configurar con "editor.maxTokenizationLineLength".'],"vs/editor/contrib/hover/browser/markerHoverParticipant":["Ver el problema","No hay correcciones r\xE1pidas disponibles","Buscando correcciones r\xE1pidas...","No hay correcciones r\xE1pidas disponibles","Correcci\xF3n R\xE1pida"],"vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace":["Reemplazar con el valor anterior","Reemplazar con el valor siguiente"],"vs/editor/contrib/indentation/browser/indentation":["Convertir sangr\xEDa en espacios","Convertir sangr\xEDa en tabulaciones","Tama\xF1o de tabulaci\xF3n configurado","Tama\xF1o de tabulaci\xF3n predeterminado","Tama\xF1o de tabulaci\xF3n actual","Seleccionar tama\xF1o de tabulaci\xF3n para el archivo actual","Aplicar sangr\xEDa con tabulaciones","Aplicar sangr\xEDa con espacios","Cambiar tama\xF1o de visualizaci\xF3n de tabulaci\xF3n","Detectar sangr\xEDa del contenido","Volver a aplicar sangr\xEDa a l\xEDneas","Volver a aplicar sangr\xEDa a l\xEDneas seleccionadas"],"vs/editor/contrib/inlayHints/browser/inlayHintsHover":["Haga doble clic para insertar","cmd + clic","ctrl + clic","opci\xF3n + clic","alt + clic","Ir a Definici\xF3n ({0}), haga clic con el bot\xF3n derecho para obtener m\xE1s informaci\xF3n","Ir a Definici\xF3n ({0})","Ejecutar comando"],"vs/editor/contrib/inlineCompletions/browser/commands":["Mostrar sugerencia alineada siguiente","Mostrar sugerencia alineada anterior","Desencadenar sugerencia alineada","Aceptar la siguiente palabra de sugerencia insertada","Aceptar palabra","Aceptar la siguiente l\xEDnea de sugerencia insertada","Aceptar l\xEDnea","Aceptar la sugerencia insertada","Aceptar","Ocultar la sugerencia insertada","Mostrar siempre la barra de herramientas"],"vs/editor/contrib/inlineCompletions/browser/hoverParticipant":["Sugerencia:"],"vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys":["Si una sugerencia alineada est\xE1 visible","Si la sugerencia alineada comienza con un espacio en blanco","Si la sugerencia insertada comienza con un espacio en blanco menor que lo que se insertar\xEDa mediante tabulaci\xF3n","Si las sugerencias deben suprimirse para la sugerencia actual"],"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController":["Inspeccionar esto en la vista accesible ({0})"],"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget":["Icono para mostrar la sugerencia de par\xE1metro siguiente.","Icono para mostrar la sugerencia de par\xE1metro anterior.","{0} ({1})","Anterior","Siguiente"],"vs/editor/contrib/lineSelection/browser/lineSelection":["Expandir selecci\xF3n de l\xEDnea"],"vs/editor/contrib/linesOperations/browser/linesOperations":["Copiar l\xEDnea arriba","&&Copiar l\xEDnea arriba","Copiar l\xEDnea abajo","Co&&piar l\xEDnea abajo","Selecci\xF3n duplicada","&&Duplicar selecci\xF3n","Mover l\xEDnea hacia arriba","Mo&&ver l\xEDnea arriba","Mover l\xEDnea hacia abajo","Mover &&l\xEDnea abajo","Ordenar l\xEDneas en orden ascendente","Ordenar l\xEDneas en orden descendente","Eliminar l\xEDneas duplicadas","Recortar espacio final","Eliminar l\xEDnea","Sangr\xEDa de l\xEDnea","Anular sangr\xEDa de l\xEDnea","Insertar l\xEDnea arriba","Insertar l\xEDnea debajo","Eliminar todo a la izquierda","Eliminar todo lo que est\xE1 a la derecha","Unir l\xEDneas","Transponer caracteres alrededor del cursor","Transformar a may\xFAsculas","Transformar a min\xFAsculas","Transformar en Title Case","Transformar en Snake Case","Transformar a may\xFAsculas y min\xFAsculas Camel","Transformar en caso Kebab"],"vs/editor/contrib/linkedEditing/browser/linkedEditing":["Iniciar edici\xF3n vinculada","Color de fondo cuando el editor cambia el nombre autom\xE1ticamente al escribir."],"vs/editor/contrib/links/browser/links":["No se pudo abrir este v\xEDnculo porque no tiene un formato correcto: {0}","No se pudo abrir este v\xEDnculo porque falta el destino.","Ejecutar comando","Seguir v\xEDnculo","cmd + clic","ctrl + clic","opci\xF3n + clic","alt + clic","Ejecutar el comando {0}","Abrir v\xEDnculo"],"vs/editor/contrib/message/browser/messageController":["Indica si el editor muestra actualmente un mensaje insertado"],"vs/editor/contrib/multicursor/browser/multicursor":["Cursor agregado: {0}","Cursores agregados: {0}","Agregar cursor arriba","&&Agregar cursor arriba","Agregar cursor debajo","A&&gregar cursor abajo","A\xF1adir cursores a finales de l\xEDnea","Agregar c&&ursores a extremos de l\xEDnea","A\xF1adir cursores a la parte inferior","A\xF1adir cursores a la parte superior","Agregar selecci\xF3n hasta la siguiente coincidencia de b\xFAsqueda","Agregar &&siguiente repetici\xF3n","Agregar selecci\xF3n hasta la anterior coincidencia de b\xFAsqueda","Agregar r&&epetici\xF3n anterior","Mover \xFAltima selecci\xF3n hasta la siguiente coincidencia de b\xFAsqueda","Mover \xFAltima selecci\xF3n hasta la anterior coincidencia de b\xFAsqueda","Seleccionar todas las repeticiones de coincidencia de b\xFAsqueda","Seleccionar todas las &&repeticiones","Cambiar todas las ocurrencias","Enfocar el siguiente cursor","Centra el cursor siguiente","Enfocar cursor anterior","Centra el cursor anterior"],"vs/editor/contrib/parameterHints/browser/parameterHints":["Sugerencias para par\xE1metros Trigger"],"vs/editor/contrib/parameterHints/browser/parameterHintsWidget":["Icono para mostrar la sugerencia de par\xE1metro siguiente.","Icono para mostrar la sugerencia de par\xE1metro anterior.","{0}, sugerencia","Color de primer plano del elemento activo en la sugerencia de par\xE1metro."],"vs/editor/contrib/peekView/browser/peekView":["Indica si el editor de c\xF3digo actual est\xE1 incrustado en la inspecci\xF3n.","Cerrar","Color de fondo del \xE1rea de t\xEDtulo de la vista de inspecci\xF3n.","Color del t\xEDtulo de la vista de inpecci\xF3n.","Color de la informaci\xF3n del t\xEDtulo de la vista de inspecci\xF3n.","Color de los bordes y la flecha de la vista de inspecci\xF3n.","Color de fondo de la lista de resultados de vista de inspecci\xF3n.","Color de primer plano de los nodos de inspecci\xF3n en la lista de resultados.","Color de primer plano de los archivos de inspecci\xF3n en la lista de resultados.","Color de fondo de la entrada seleccionada en la lista de resultados de vista de inspecci\xF3n.","Color de primer plano de la entrada seleccionada en la lista de resultados de vista de inspecci\xF3n.","Color de fondo del editor de vista de inspecci\xF3n.","Color de fondo del margen en el editor de vista de inspecci\xF3n.","Color de fondo del desplazamiento permanente en el editor de vista de inspecci\xF3n.","Buscar coincidencia con el color de resaltado de la lista de resultados de vista de inspecci\xF3n.","Buscar coincidencia del color de resultado del editor de vista de inspecci\xF3n.","Hacer coincidir el borde resaltado en el editor de vista previa."],"vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess":["Abra primero un editor de texto para ir a una l\xEDnea.","Vaya a la l\xEDnea {0} y al car\xE1cter {1}.","Ir a la l\xEDnea {0}.","L\xEDnea actual: {0}, Car\xE1cter: {1}. Escriba un n\xFAmero de l\xEDnea entre 1 y {2} a los que navegar.","L\xEDnea actual: {0}, Car\xE1cter: {1}. Escriba un n\xFAmero de l\xEDnea al que navegar."],"vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess":["Para ir a un s\xEDmbolo, primero abra un editor de texto con informaci\xF3n de s\xEDmbolo.","El editor de texto activo no proporciona informaci\xF3n de s\xEDmbolos.","No hay ning\xFAn s\xEDmbolo del editor coincidente.","No hay s\xEDmbolos del editor.","Abrir en el lateral","Abrir en la parte inferior","s\xEDmbolos ({0})","propiedades ({0})","m\xE9todos ({0})","funciones ({0})","constructores ({0})","variables ({0})","clases ({0})","estructuras ({0})","eventos ({0})","operadores ({0})","interfaces ({0})","espacios de nombres ({0})","paquetes ({0})","par\xE1metros de tipo ({0})","m\xF3dulos ({0})","propiedades ({0})","enumeraciones ({0})","miembros de enumeraci\xF3n ({0})","cadenas ({0})","archivos ({0})","matrices ({0})","n\xFAmeros ({0})","booleanos ({0})","objetos ({0})","claves ({0})","campos ({0})","constantes ({0})"],"vs/editor/contrib/readOnlyMessage/browser/contribution":["No se puede editar en la entrada de solo lectura","No se puede editar en un editor de s\xF3lo lectura"],"vs/editor/contrib/rename/browser/rename":["No hay ning\xFAn resultado.","Error desconocido al resolver el cambio de nombre de la ubicaci\xF3n","Cambiando el nombre de '{0}' a '{1}'","Cambiar el nombre de {0} a {1}","Nombre cambiado correctamente de '{0}' a '{1}'. Resumen: {2}","No se pudo cambiar el nombre a las ediciones de aplicaci\xF3n","No se pudo cambiar el nombre de las ediciones de c\xE1lculo","Cambiar el nombre del s\xEDmbolo","Activar/desactivar la capacidad de previsualizar los cambios antes de cambiar el nombre"],"vs/editor/contrib/rename/browser/renameInputField":["Indica si el widget de cambio de nombre de entrada est\xE1 visible.","Cambie el nombre de la entrada. Escriba el nuevo nombre y presione Entrar para confirmar.","{0} para cambiar de nombre, {1} para obtener una vista previa"],"vs/editor/contrib/smartSelect/browser/smartSelect":["Expandir selecci\xF3n","&&Expandir selecci\xF3n","Reducir la selecci\xF3n","&&Reducir selecci\xF3n"],"vs/editor/contrib/snippet/browser/snippetController2":["Indica si el editor actual est\xE1 en modo de fragmentos de c\xF3digo.","Indica si hay una tabulaci\xF3n siguiente cuando se est\xE1 en modo de fragmentos de c\xF3digo.","Si hay una tabulaci\xF3n anterior cuando se est\xE1 en modo de fragmentos de c\xF3digo.","Ir al marcador de posici\xF3n siguiente..."],"vs/editor/contrib/snippet/browser/snippetVariables":["Domingo","Lunes","Martes","Mi\xE9rcoles","Jueves","Viernes","S\xE1bado","Dom","Lun","Mar","Mi\xE9","Jue","Vie","S\xE1b","Enero","Febrero","Marzo","Abril","May","Junio","Julio","Agosto","Septiembre","Octubre","Noviembre","Diciembre","Ene","Feb","Mar","Abr","May","Jun","Jul","Ago","Sep","Oct","Nov","Dic"],"vs/editor/contrib/stickyScroll/browser/stickyScrollActions":["Alternar desplazamiento permanente","&&Alternar desplazamiento permanente","Desplazamiento permanente","&&Desplazamiento permanente","Desplazamiento permanente de foco","&&Desplazamiento permanente de foco","Seleccionar la siguiente l\xEDnea de desplazamiento r\xE1pida","Seleccionar la l\xEDnea de desplazamiento r\xE1pida anterior","Ir a la l\xEDnea de desplazamiento r\xE1pida con foco","Seleccionar el Editor"],"vs/editor/contrib/suggest/browser/suggest":["Si alguna sugerencia tiene el foco","Indica si los detalles de las sugerencias est\xE1n visibles.","Indica si hay varias sugerencias para elegir.","Indica si la inserci\xF3n de la sugerencia actual genera un cambio o si ya se ha escrito todo.","Indica si se insertan sugerencias al presionar Entrar.","Indica si la sugerencia actual tiene el comportamiento de inserci\xF3n y reemplazo.","Indica si el comportamiento predeterminado es insertar o reemplazar.","Indica si la sugerencia actual admite la resoluci\xF3n de m\xE1s detalles."],"vs/editor/contrib/suggest/browser/suggestController":['Aceptando "{0}" ediciones adicionales de {1} realizadas',"Sugerencias para Trigger","Insertar","Insertar","Reemplazar","Reemplazar","Insertar","mostrar menos","mostrar m\xE1s","Restablecer tama\xF1o del widget de sugerencias"],"vs/editor/contrib/suggest/browser/suggestWidget":["Color de fondo del widget sugerido.","Color de borde del widget sugerido.","Color de primer plano del widget sugerido.","Color de primer plano de le entrada seleccionada del widget de sugerencias.","Color de primer plano del icono de la entrada seleccionada en el widget de sugerencias.","Color de fondo de la entrada seleccionada del widget sugerido.","Color del resaltado coincidido en el widget sugerido.","Color de los resaltados de coincidencia en el widget de sugerencias cuando se enfoca un elemento.","Color de primer plano del estado del widget sugerido.","Cargando...","No hay sugerencias.","Sugerir","{0} {1}, {2}","{0} {1}","{0}, {1}","{0}, documentos: {1}"],"vs/editor/contrib/suggest/browser/suggestWidgetDetails":["Cerrar","Cargando..."],"vs/editor/contrib/suggest/browser/suggestWidgetRenderer":["Icono para obtener m\xE1s informaci\xF3n en el widget de sugerencias.","Leer m\xE1s"],"vs/editor/contrib/suggest/browser/suggestWidgetStatus":["{0} ({1})"],"vs/editor/contrib/symbolIcons/browser/symbolIcons":["Color de primer plano de los s\xEDmbolos de matriz. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos booleanos. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de clase. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de color. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos constantes. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de constructor. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de enumerador. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de miembro del enumerador. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de evento. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de campo. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de archivo. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de carpeta. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de funci\xF3n. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de interfaz. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de claves. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de palabra clave. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de m\xE9todo. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de m\xF3dulo. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de espacio de nombres. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos nulos. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano para los s\xEDmbolos num\xE9ricos. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de objeto. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano para los s\xEDmbolos del operador. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de paquete. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de propiedad. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de referencia. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de fragmento de c\xF3digo. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de cadena. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de estructura. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de texto. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano para los s\xEDmbolos de par\xE1metro de tipo. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos de unidad. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias.","Color de primer plano de los s\xEDmbolos variables. Estos s\xEDmbolos aparecen en el contorno, la ruta de navegaci\xF3n y el widget de sugerencias."],"vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode":["Alternar tecla de tabulaci\xF3n para mover el punto de atenci\xF3n","Presionando la pesta\xF1a ahora mover\xE1 el foco al siguiente elemento enfocable.","Presionando la pesta\xF1a ahora insertar\xE1 el car\xE1cter de tabulaci\xF3n"],"vs/editor/contrib/tokenization/browser/tokenization":["Desarrollador: forzar nueva aplicaci\xF3n de token"],"vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter":["Icono que se muestra con un mensaje de advertencia en el editor de extensiones.","Este documento contiene muchos caracteres Unicode ASCII no b\xE1sicos","Este documento contiene muchos caracteres Unicode ambiguos","Este documento contiene muchos caracteres Unicode invisibles","El car\xE1cter {0} podr\xEDa confundirse con el car\xE1cter ASCII {1}, que es m\xE1s com\xFAn en el c\xF3digo fuente.","El car\xE1cter {0} podr\xEDa confundirse con el car\xE1cter {1}, que es m\xE1s com\xFAn en el c\xF3digo fuente.","El car\xE1cter {0} es invisible.","El car\xE1cter {0} no es un car\xE1cter ASCII b\xE1sico.","Ajustar la configuraci\xF3n","Deshabilitar resaltado en comentarios","Deshabilitar resaltado de caracteres en comentarios","Deshabilitar resaltado en cadenas","Deshabilitar resaltado de caracteres en cadenas","Deshabilitar resaltado ambiguo","Deshabilitar el resaltado de caracteres ambiguos","Deshabilitar resaltado invisible","Deshabilitar el resaltado de caracteres invisibles","Deshabilitar resaltado que no es ASCII","Deshabilitar el resaltado de caracteres ASCII no b\xE1sicos","Mostrar opciones de exclusi\xF3n","Excluir {0} (car\xE1cter invisible) de que se resalte","Excluir {0} de ser resaltado",'Permite caracteres Unicode m\xE1s comunes en el idioma "{0}".',"Configurar opciones de resaltado Unicode"],"vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators":["Terminadores de l\xEDnea inusuales","Se han detectado terminadores de l\xEDnea inusuales",`Este archivo "{0}" contiene uno o m\xE1s caracteres de terminaci\xF3n de l\xEDnea inusuales, como el separador de l\xEDnea (LS) o el separador de p\xE1rrafo (PS).\r
\r
Se recomienda eliminarlos del archivo. Esto puede configurarse mediante "editor.unusualLineTerminators".`,"&&Quitar terminadores de l\xEDnea inusuales","Omitir"],"vs/editor/contrib/wordHighlighter/browser/highlightDecorations":["Color de fondo de un s\xEDmbolo durante el acceso de lectura, como la lectura de una variable. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de fondo de un s\xEDmbolo durante el acceso de escritura, como escribir en una variable. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de fondo de la presencia textual para un s\xEDmbolo. Para evitar ocultar cualquier decoraci\xF3n subyacente, el color no debe ser opaco.","Color de fondo de un s\xEDmbolo durante el acceso de lectura; por ejemplo, cuando se lee una variable.","Color de fondo de un s\xEDmbolo durante el acceso de escritura; por ejemplo, cuando se escribe una variable.","Color de borde de una repetici\xF3n textual de un s\xEDmbolo.","Color del marcador de regla general para destacados de s\xEDmbolos. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de marcador de regla general para destacados de s\xEDmbolos de acceso de escritura. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color del marcador de regla de informaci\xF3n general de una repetici\xF3n textual de un s\xEDmbolo. El color no debe ser opaco para no ocultar las decoraciones subyacentes."],"vs/editor/contrib/wordHighlighter/browser/wordHighlighter":["Ir al siguiente s\xEDmbolo destacado","Ir al s\xEDmbolo destacado anterior","Desencadenar los s\xEDmbolos destacados"],"vs/editor/contrib/wordOperations/browser/wordOperations":["Eliminar palabra"],"vs/platform/action/common/actionCommonCategories":["Ver","Ayuda","Probar","archivo","Preferencias","Desarrollador"],"vs/platform/actionWidget/browser/actionList":["{0} para aplicar, {1} para previsualizar","{0} para aplicar","{0}, Motivo de deshabilitaci\xF3n: {1}","Widget de acci\xF3n"],"vs/platform/actionWidget/browser/actionWidget":["Color de fondo de los elementos de acci\xF3n alternados en la barra de acciones.","Si la lista de widgets de acci\xF3n es visible","Ocultar el widget de acci\xF3n","Seleccione la acci\xF3n anterior","Seleccione la siguiente acci\xF3n","Aceptar la acci\xF3n seleccionada","Vista previa de la acci\xF3n seleccionada"],"vs/platform/actions/browser/menuEntryActionViewItem":["{0} ({1})","{0} ({1})",`{0}\r
[{1}] {2}`],"vs/platform/actions/browser/toolbar":["Ocultar","Men\xFA Restablecer"],"vs/platform/actions/common/menuService":['Ocultar "{0}"'],"vs/platform/audioCues/browser/audioCueService":["Error en la l\xEDnea","Advertencia en la l\xEDnea","\xC1rea doblada en la l\xEDnea","Punto de interrupci\xF3n en la l\xEDnea","Sugerencia insertada en la l\xEDnea","Correcci\xF3n r\xE1pida del terminal","Depurador detenido en el punto de interrupci\xF3n","No hay sugerencias de incrustaci\xF3n en la l\xEDnea","Tarea completada.","Error en la tarea","Error del comando de terminal","Campana de terminal","Celda del bloc de notas completada","Error en la celda del bloc de notas","L\xEDnea de diferencia insertada","L\xEDnea de diferencia eliminada","L\xEDnea de diferencia modificada","Se envi\xF3 una solicitud de chat","Respuesta de chat recibida","Respuesta de chat pendiente","Borrar","Guardar","Formato"],"vs/platform/configuration/common/configurationRegistry":["La configuraci\xF3n del lenguaje predeterminada se reemplaza","Configure los valores que se invalidar\xE1n para el idioma {0}.","Establecer los valores de configuraci\xF3n que se reemplazar\xE1n para un lenguaje.","Esta configuraci\xF3n no admite la configuraci\xF3n por idioma.","Establecer los valores de configuraci\xF3n que se reemplazar\xE1n para un lenguaje.","Esta configuraci\xF3n no admite la configuraci\xF3n por idioma.","No se puede registrar una propiedad vac\xEDa.",`No se puede registrar "{0}". Coincide con el patr\xF3n de propiedad '\\\\[.*\\\\]$' para describir la configuraci\xF3n del editor espec\xEDfica del lenguaje. Utilice la contribuci\xF3n "configurationDefaults".`,'No se puede registrar "{0}". Esta propiedad ya est\xE1 registrada.','No se puede registrar "{0}". La directiva asociada {1} ya est\xE1 registrada con {2}.'],"vs/platform/contextkey/browser/contextKeyService":["Comando que devuelve informaci\xF3n sobre las claves de contexto"],"vs/platform/contextkey/common/contextkey":["Expresi\xF3n de clave de contexto vac\xEDa",'\xBFHa olvidado escribir una expresi\xF3n? tambi\xE9n puede poner "false" o "true" para evaluar siempre como false o true, respectivamente.',"'in' despu\xE9s de 'not'.","par\xE9ntesis de cierre ')'","Token inesperado","\xBFHa olvidado poner && o || antes del token?","Final de expresi\xF3n inesperado","\xBFHa olvidado poner una clave de contexto?",`Esperado: {0}\r
recibido: '{1}'.`],"vs/platform/contextkey/common/contextkeys":["Si el sistema operativo es macOS","Si el sistema operativo es Linux","Si el sistema operativo es Windows","Si la plataforma es un explorador web","Si el sistema operativo es macOS en una plataforma que no es de explorador","Si el sistema operativo es IOS","Si la plataforma es un explorador web m\xF3vil","Tipo de calidad de VS Code","Si el foco del teclado est\xE1 dentro de un cuadro de entrada"],"vs/platform/contextkey/common/scanner":["\xBFQuiso decir {0}?","\xBFQuiso decir {0} o {1}?","\xBFQuiso decir {0}, {1} o {2}?","\xBFHa olvidado abrir o cerrar la cita?",`\xBFHa olvidado escapar el car\xE1cter "/" (barra diagonal)?Coloque dos barras diagonales inversas antes de que escape, por ejemplo, '\\\\/'.`],"vs/platform/history/browser/contextScopedHistoryWidget":["Indica si las sugerencias est\xE1n visibles."],"vs/platform/keybinding/common/abstractKeybindingService":["Se presion\xF3 ({0}). Esperando la siguiente tecla...","Se ha presionado ({0}). Esperando la siguiente tecla...","La combinaci\xF3n de claves ({0}, {1}) no es un comando.","La combinaci\xF3n de claves ({0}, {1}) no es un comando."],"vs/platform/list/browser/listService":["\xC1rea de trabajo",'Se asigna a "Control" en Windows y Linux y a "Comando" en macOS.','Se asigna a "Alt" en Windows y Linux y a "Opci\xF3n" en macOS.',"El modificador que se utilizar\xE1 para agregar un elemento en los \xE1rboles y listas para una selecci\xF3n m\xFAltiple con el rat\xF3n (por ejemplo en el explorador, abiertos editores y vista de scm). Los gestos de rat\xF3n 'Abrir hacia' - si est\xE1n soportados - se adaptar\xE1n de forma tal que no tenga conflicto con el modificador m\xFAltiple.","Controla c\xF3mo abrir elementos en los \xE1rboles y las listas mediante el mouse (si se admite). Tenga en cuenta que algunos \xE1rboles y listas pueden optar por ignorar esta configuraci\xF3n si no es aplicable.","Controla si las listas y los \xE1rboles admiten el desplazamiento horizontal en el \xE1rea de trabajo. Advertencia: La activaci\xF3n de esta configuraci\xF3n repercute en el rendimiento.","Controla si los clics en la barra de desplazamiento se desplazan p\xE1gina por p\xE1gina.","Controla la sangr\xEDa de \xE1rbol en p\xEDxeles.","Controla si el \xE1rbol debe representar gu\xEDas de sangr\xEDa.","Controla si las listas y los \xE1rboles tienen un desplazamiento suave.",'Se usar\xE1 un multiplicador en los eventos de desplazamiento de la rueda del mouse "deltaX" y "deltaY". ','Multiplicador de la velocidad de desplazamiento al presionar "Alt".',"Resalta elementos al buscar. Navegar m\xE1s arriba o abajo pasar\xE1 solo por los elementos resaltados.","Filtre elementos al buscar.","Controla el modo de b\xFAsqueda predeterminado para listas y \xE1rboles en el \xE1rea de trabajo.","La navegaci\xF3n simple del teclado se centra en elementos que coinciden con la entrada del teclado. El emparejamiento se hace solo en prefijos.","Destacar la navegaci\xF3n del teclado resalta los elementos que coinciden con la entrada del teclado. M\xE1s arriba y abajo la navegaci\xF3n atravesar\xE1 solo los elementos destacados.","La navegaci\xF3n mediante el teclado de filtro filtrar\xE1 y ocultar\xE1 todos los elementos que no coincidan con la entrada del teclado.","Controla el estilo de navegaci\xF3n del teclado para listas y \xE1rboles en el \xE1rea de trabajo. Puede ser simple, resaltar y filtrar.",'Use "workbench.list.defaultFindMode" y "workbench.list.typeNavigationMode" en su lugar.',"Usar coincidencias aproximadas al buscar.","Use coincidencias contiguas al buscar.","Controla el tipo de coincidencia que se usa al buscar listas y \xE1rboles en el \xE1rea de trabajo.","Controla c\xF3mo se expanden las carpetas de \xE1rbol al hacer clic en sus nombres. Tenga en cuenta que algunos \xE1rboles y listas pueden optar por omitir esta configuraci\xF3n si no es aplicable.","Controla si el desplazamiento permanente est\xE1 habilitado en los \xE1rboles.","Controla el n\xFAmero de elementos permanentes que se muestran en el \xE1rbol cuando '#workbench.tree.enableStickyScroll#' est\xE1 habilitado.",'Controla el funcionamiento de la navegaci\xF3n por tipos en listas y \xE1rboles del \xE1rea de trabajo. Cuando se establece en "trigger", la navegaci\xF3n por tipos comienza una vez que se ejecuta el comando "list.triggerTypeNavigation".'],"vs/platform/markers/common/markers":["Error","Advertencia","Informaci\xF3n"],"vs/platform/quickinput/browser/commandsQuickAccess":["usado recientemente","comandos similares","usados habitualmente","otros comandos","comandos similares","{0}, {1}",'El comando "{0}" ha dado lugar a un error'],"vs/platform/quickinput/browser/helpQuickAccess":["{0}, {1}"],"vs/platform/quickinput/browser/quickInput":["Atr\xE1s",'Presione "Entrar" para confirmar su entrada o "Esc" para cancelar',"{0}/{1}","Escriba para restringir los resultados."],"vs/platform/quickinput/browser/quickInputController":["Activar o desactivar todas las casillas","{0} resultados","{0} seleccionados","Aceptar","Personalizado","Atr\xE1s ({0})","Atr\xE1s"],"vs/platform/quickinput/browser/quickInputList":["Entrada r\xE1pida"],"vs/platform/quickinput/browser/quickInputUtils":['Haga clic en para ejecutar el comando "{0}"'],"vs/platform/theme/common/colorRegistry":["Color de primer plano general. Este color solo se usa si un componente no lo invalida.","Primer plano general de los elementos deshabilitados. Este color solo se usa si un componente no lo reemplaza.","Color de primer plano general para los mensajes de erroe. Este color solo se usa si un componente no lo invalida.","Color de primer plano para el texto descriptivo que proporciona informaci\xF3n adicional, por ejemplo para una etiqueta.","El color predeterminado para los iconos en el \xE1rea de trabajo.","Color de borde de los elementos con foco. Este color solo se usa si un componente no lo invalida.","Un borde adicional alrededor de los elementos para separarlos unos de otros y as\xED mejorar el contraste.","Un borde adicional alrededor de los elementos activos para separarlos unos de otros y as\xED mejorar el contraste.","El color de fondo del texto seleccionado en el \xE1rea de trabajo (por ejemplo, campos de entrada o \xE1reas de texto). Esto no se aplica a las selecciones dentro del editor.","Color para los separadores de texto.","Color de primer plano para los v\xEDnculos en el texto.","Color de primer plano para los enlaces de texto, al hacer clic o pasar el mouse sobre ellos.","Color de primer plano para los segmentos de texto con formato previo.","Color de fondo para segmentos de texto con formato previo.","Color de fondo para los bloques en texto.","Color de borde para los bloques en texto.","Color de fondo para los bloques de c\xF3digo en el texto.","Color de sombra de los widgets dentro del editor, como buscar/reemplazar","Color de borde de los widgets dentro del editor, como buscar/reemplazar","Fondo de cuadro de entrada.","Primer plano de cuadro de entrada.","Borde de cuadro de entrada.","Color de borde de opciones activadas en campos de entrada.","Color de fondo de las opciones activadas en los campos de entrada.","Color de fondo al pasar por encima de las opciones en los campos de entrada.","Color de primer plano de las opciones activadas en los campos de entrada.","Color de primer plano para el marcador de posici\xF3n de texto","Color de fondo de validaci\xF3n de entrada para gravedad de informaci\xF3n.","Color de primer plano de validaci\xF3n de entrada para informaci\xF3n de gravedad.","Color de borde de validaci\xF3n de entrada para gravedad de informaci\xF3n.","Color de fondo de validaci\xF3n de entrada para gravedad de advertencia.","Color de primer plano de validaci\xF3n de entrada para informaci\xF3n de advertencia.","Color de borde de validaci\xF3n de entrada para gravedad de advertencia.","Color de fondo de validaci\xF3n de entrada para gravedad de error.","Color de primer plano de validaci\xF3n de entrada para informaci\xF3n de error.","Color de borde de valdaci\xF3n de entrada para gravedad de error.","Fondo de lista desplegable.","Fondo de la lista desplegable.","Primer plano de lista desplegable.","Borde de lista desplegable.","Color de primer plano del bot\xF3n.","Color del separador de botones.","Color de fondo del bot\xF3n.","Color de fondo del bot\xF3n al mantener el puntero.","Color del borde del bot\xF3n","Color de primer plano del bot\xF3n secundario.","Color de fondo del bot\xF3n secundario.","Color de fondo del bot\xF3n secundario al mantener el mouse.","Color de fondo de la insignia. Las insignias son peque\xF1as etiquetas de informaci\xF3n, por ejemplo los resultados de un n\xFAmero de resultados.","Color de primer plano de la insignia. Las insignias son peque\xF1as etiquetas de informaci\xF3n, por ejemplo los resultados de un n\xFAmero de resultados.","Sombra de la barra de desplazamiento indica que la vista se ha despazado.","Color de fondo de control deslizante de barra de desplazamiento.","Color de fondo de barra de desplazamiento cursor cuando se pasar sobre el control.","Color de fondo de la barra de desplazamiento al hacer clic.","Color de fondo para la barra de progreso que se puede mostrar para las operaciones de larga duraci\xF3n.","Color de fondo del texto de error del editor. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de primer plano de squigglies de error en el editor.","Si se establece, color de subrayados dobles para errores en el editor.","Color de fondo del texto de advertencia del editor. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de primer plano de squigglies de advertencia en el editor.","Si se establece, color de subrayados dobles para advertencias en el editor.","Color de fondo del texto de informaci\xF3n del editor. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de primer plano de los subrayados ondulados informativos en el editor.","Si se establece, color de subrayados dobles para informaciones en el editor.","Color de primer plano de pista squigglies en el editor.","Si se establece, color de subrayados dobles para sugerencias en el editor.","Color de borde de los marcos activos.","Color de fondo del editor.","Color de primer plano predeterminado del editor.","Color de fondo de desplazamiento permanente para el editor","Desplazamiento permanente al mantener el mouse sobre el color de fondo del editor","Color de fondo del editor de widgets como buscar/reemplazar","Color de primer plano de los widgets del editor, como buscar y reemplazar.","Color de borde de los widgets del editor. El color solo se usa si el widget elige tener un borde y no invalida el color.","Color del borde de la barra de cambio de tama\xF1o de los widgets del editor. El color se utiliza solo si el widget elige tener un borde de cambio de tama\xF1o y si un widget no invalida el color.","Color de fondo del selector r\xE1pido. El widget del selector r\xE1pido es el contenedor para selectores como la paleta de comandos.","Color de primer plano del selector r\xE1pido. El widget del selector r\xE1pido es el contenedor para selectores como la paleta de comandos.","Color de fondo del t\xEDtulo del selector r\xE1pido. El widget del selector r\xE1pido es el contenedor para selectores como la paleta de comandos.","Selector de color r\xE1pido para la agrupaci\xF3n de etiquetas.","Selector de color r\xE1pido para la agrupaci\xF3n de bordes.","Color de fondo de etiqueta de enlace de teclado. La etiqueta enlace de teclado se usa para representar un m\xE9todo abreviado de teclado.","Color de primer plano de etiqueta de enlace de teclado. La etiqueta enlace de teclado se usa para representar un m\xE9todo abreviado de teclado.","Color del borde de la etiqueta de enlace de teclado. La etiqueta enlace de teclado se usa para representar un m\xE9todo abreviado de teclado.","Color del borde inferior de la etiqueta de enlace de teclado. La etiqueta enlace de teclado se usa para representar un m\xE9todo abreviado de teclado.","Color de la selecci\xF3n del editor.","Color del texto seleccionado para alto contraste.","Color de la selecci\xF3n en un editor inactivo. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color en las regiones con el mismo contenido que la selecci\xF3n. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de borde de las regiones con el mismo contenido que la selecci\xF3n.","Color de la coincidencia de b\xFAsqueda actual.","Color de los otros resultados de la b\xFAsqueda. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de la gama que limita la b\xFAsqueda. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de borde de la coincidencia de b\xFAsqueda actual.","Color de borde de otra b\xFAsqueda que coincide.","Color del borde de la gama que limita la b\xFAsqueda. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de las consultas coincidentes del Editor de b\xFAsqueda.","Color de borde de las consultas coincidentes del Editor de b\xFAsqueda.","Color del texto en el mensaje de finalizaci\xF3n del viewlet de b\xFAsqueda.","Destacar debajo de la palabra para la que se muestra un mensaje al mantener el mouse. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de fondo al mantener el puntero en el editor.","Color de primer plano al mantener el puntero en el editor.","Color del borde al mantener el puntero en el editor.","Color de fondo de la barra de estado al mantener el puntero en el editor.","Color de los v\xEDnculos activos.","Color de primer plano de las sugerencias insertadas","Color de fondo de las sugerencias insertadas","Color de primer plano de las sugerencias insertadas para los tipos de letra","Color de fondo de las sugerencias insertadas para los tipos de letra","Color de primer plano de las sugerencias insertadas para los par\xE1metros","Color de fondo de las sugerencias insertadas para los par\xE1metros","El color utilizado para el icono de bombilla de acciones.","El color utilizado para el icono de la bombilla de acciones de correcci\xF3n autom\xE1tica.","El color utilizado para el icono de bombilla de inteligencia artificial.","Color de fondo para el texto que se insert\xF3. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de fondo para el texto que se elimin\xF3. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de fondo de las l\xEDneas insertadas. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de fondo de las l\xEDneas que se quitaron. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de fondo del margen donde se insertaron las l\xEDneas.","Color de fondo del margen donde se quitaron las l\xEDneas.","Primer plano de la regla de informaci\xF3n general de diferencias para el contenido insertado.","Primer plano de la regla de informaci\xF3n general de diferencias para el contenido quitado.","Color de contorno para el texto insertado.","Color de contorno para el texto quitado.","Color del borde entre ambos editores de texto.","Color de relleno diagonal del editor de diferencias. El relleno diagonal se usa en las vistas de diferencias en paralelo.","Color de fondo de los bloques sin modificar en el editor de diferencias.","Color de primer plano de los bloques sin modificar en el editor de diferencias.","Color de fondo del c\xF3digo sin modificar en el editor de diferencias.","Color de fondo de la lista o el \xE1rbol del elemento con el foco cuando la lista o el \xE1rbol est\xE1n activos. Una lista o un \xE1rbol tienen el foco del teclado cuando est\xE1n activos, cuando est\xE1n inactivos no.","Color de primer plano de la lista o el \xE1rbol del elemento con el foco cuando la lista o el \xE1rbol est\xE1n activos. Una lista o un \xE1rbol tienen el foco del teclado cuando est\xE1n activos, cuando est\xE1n inactivos no.","Color de contorno de la lista o el \xE1rbol del elemento con el foco cuando la lista o el \xE1rbol est\xE1n activos. Una lista o un \xE1rbol tienen el foco del teclado cuando est\xE1n activos, pero no cuando est\xE1n inactivos.","Color de contorno de la lista o el \xE1rbol del elemento con el foco cuando la lista o el \xE1rbol est\xE1n activos y seleccionados. Una lista o un \xE1rbol tienen el foco del teclado cuando est\xE1n activos, pero no cuando est\xE1n inactivos.","Color de fondo de la lista o el \xE1rbol del elemento seleccionado cuando la lista o el \xE1rbol est\xE1n activos. Una lista o un \xE1rbol tienen el foco del teclado cuando est\xE1n activos, cuando est\xE1n inactivos no.","Color de primer plano de la lista o el \xE1rbol del elemento seleccionado cuando la lista o el \xE1rbol est\xE1n activos. Una lista o un \xE1rbol tienen el foco del teclado cuando est\xE1n activos, cuando est\xE1n inactivos no.","Color de primer plano del icono de lista o \xE1rbol del elemento seleccionado cuando la lista o el \xE1rbol est\xE1n activos. Una lista o un \xE1rbol tienen el foco del teclado cuando est\xE1n activos, cuando est\xE1n inactivos no.","Color de fondo de la lista o el \xE1rbol del elemento seleccionado cuando la lista o el \xE1rbol est\xE1n inactivos. Una lista o un \xE1rbol tienen el foco del teclado cuando est\xE1n activos, cuando est\xE1n inactivos no.","Color de primer plano de la lista o el \xE1rbol del elemento con el foco cuando la lista o el \xE1rbol esta inactiva. Una lista o un \xE1rbol tiene el foco del teclado cuando est\xE1 activo, cuando esta inactiva no.","Color de primer plano del icono de lista o \xE1rbol del elemento seleccionado cuando la lista o el \xE1rbol est\xE1n inactivos. Una lista o un \xE1rbol tienen el foco del teclado cuando est\xE1n activos, cuando est\xE1n inactivos no.","Color de fondo de la lista o el \xE1rbol del elemento con el foco cuando la lista o el \xE1rbol est\xE1n inactivos. Una lista o un \xE1rbol tienen el foco del teclado cuando est\xE1n activos, pero no cuando est\xE1n inactivos.","Color de contorno de la lista o el \xE1rbol del elemento con el foco cuando la lista o el \xE1rbol est\xE1n inactivos. Una lista o un \xE1rbol tienen el foco del teclado cuando est\xE1n activos, pero no cuando est\xE1n inactivos.","Fondo de la lista o el \xE1rbol al mantener el mouse sobre los elementos.","Color de primer plano de la lista o el \xE1rbol al pasar por encima de los elementos con el rat\xF3n.","Fondo de arrastrar y colocar la lista o el \xE1rbol al mover los elementos con el mouse.","Color de primer plano de la lista o el \xE1rbol de las coincidencias resaltadas al buscar dentro de la lista o el \xE1bol.","Color de primer plano de la lista o \xE1rbol de los elementos coincidentes en los elementos enfocados activamente cuando se busca dentro de la lista o \xE1rbol.","Color de primer plano de una lista o \xE1rbol para los elementos inv\xE1lidos, por ejemplo una raiz sin resolver en el explorador.","Color del primer plano de elementos de lista que contienen errores.","Color del primer plano de elementos de lista que contienen advertencias.","Color de fondo del widget de filtro de tipo en listas y \xE1rboles.","Color de contorno del widget de filtro de tipo en listas y \xE1rboles.","Color de contorno del widget de filtro de tipo en listas y \xE1rboles, cuando no hay coincidencias.","Color de sombra del widget de filtrado de escritura en listas y \xE1rboles.","Color de fondo de la coincidencia filtrada.","Color de borde de la coincidencia filtrada.","Color de trazo de \xE1rbol para las gu\xEDas de sangr\xEDa.","Color de trazo de \xE1rbol para las gu\xEDas de sangr\xEDa que no est\xE1n activas.","Color de borde de la tabla entre columnas.","Color de fondo para las filas de tabla impares.","Color de primer plano de lista/\xE1rbol para los elementos no enfatizados.","Color de fondo de la casilla de verificaci\xF3n del widget.","Color de fondo del widget de la casilla cuando se selecciona el elemento en el que se encuentra.","Color de primer plano del widget de la casilla de verificaci\xF3n.","Color del borde del widget de la casilla de verificaci\xF3n.","Color de borde del widget de la casilla cuando se selecciona el elemento en el que se encuentra.","Use quickInputList.focusBackground en su lugar.","Selector r\xE1pido del color de primer plano para el elemento con el foco.","Color de primer plano del icono del selector r\xE1pido para el elemento con el foco.","Color de fondo del selector r\xE1pido para el elemento con el foco.","Color del borde de los men\xFAs.","Color de primer plano de los elementos de men\xFA.","Color de fondo de los elementos de men\xFA.","Color de primer plano del menu para el elemento del men\xFA seleccionado.","Color de fondo del menu para el elemento del men\xFA seleccionado.","Color del borde del elemento seleccionado en los men\xFAs.","Color del separador del menu para un elemento del men\xFA.","El fondo de la barra de herramientas se perfila al pasar por encima de las acciones con el mouse.","La barra de herramientas se perfila al pasar por encima de las acciones con el mouse.","Fondo de la barra de herramientas al mantener el mouse sobre las acciones","Resaltado del color de fondo para una ficha de un fragmento de c\xF3digo.","Resaltado del color del borde para una ficha de un fragmento de c\xF3digo.","Resaltado del color de fondo para la \xFAltima ficha de un fragmento de c\xF3digo.","Resaltado del color del borde para la \xFAltima tabulaci\xF3n de un fragmento de c\xF3digo.","Color de los elementos de ruta de navegaci\xF3n que reciben el foco.","Color de fondo de los elementos de ruta de navegaci\xF3n","Color de los elementos de ruta de navegaci\xF3n que reciben el foco.","Color de los elementos de ruta de navegaci\xF3n seleccionados.","Color de fondo del selector de elementos de ruta de navegaci\xF3n.","Fondo del encabezado actual en los conflictos de combinaci\xF3n en l\xEDnea. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Fondo de contenido actual en los conflictos de combinaci\xF3n en l\xEDnea. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Fondo de encabezado entrante en los conflictos de combinaci\xF3n en l\xEDnea. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Fondo de contenido entrante en los conflictos de combinaci\xF3n en l\xEDnea. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Fondo de cabecera de elemento antecesor com\xFAn en conflictos de fusi\xF3n en l\xEDnea. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Fondo de contenido antecesor com\xFAn en conflictos de combinaci\xF3n en l\xEDnea. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color del borde en los encabezados y el divisor en conflictos de combinaci\xF3n alineados.","Primer plano de la regla de visi\xF3n general actual para conflictos de combinaci\xF3n alineados.","Primer plano de regla de visi\xF3n general de entrada para conflictos de combinaci\xF3n alineados.","Primer plano de la regla de visi\xF3n general de ancestros comunes para conflictos de combinaci\xF3n alineados.","Color del marcador de regla general para buscar actualizaciones. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color del marcador de la regla general para los destacados de la selecci\xF3n. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de marcador de minimapa para coincidencias de b\xFAsqueda.","Color de marcador de minimapa para las selecciones del editor que se repiten.","Color del marcador de minimapa para la selecci\xF3n del editor.","Color del marcador de minimapa para informaci\xF3n.","Color del marcador de minimapa para advertencias.","Color del marcador de minimapa para errores.","Color de fondo del minimapa.",'Opacidad de los elementos de primer plano representados en el minimapa. Por ejemplo, "#000000c0" representar\xE1 los elementos con 75% de opacidad.',"Color de fondo del deslizador del minimapa.","Color de fondo del deslizador del minimapa al pasar el puntero.","Color de fondo del deslizador de minimapa al hacer clic en \xE9l.","Color utilizado para el icono de error de problemas.","Color utilizado para el icono de advertencia de problemas.","Color utilizado para el icono de informaci\xF3n de problemas.","Color de primer plano que se usa en los gr\xE1ficos.","Color que se usa para las l\xEDneas horizontales en los gr\xE1ficos.","Color rojo que se usa en las visualizaciones de gr\xE1ficos.","Color azul que se usa en las visualizaciones de gr\xE1ficos.","Color amarillo que se usa en las visualizaciones de gr\xE1ficos.","Color naranja que se usa en las visualizaciones de gr\xE1ficos.","Color verde que se usa en las visualizaciones de gr\xE1ficos.","Color p\xFArpura que se usa en las visualizaciones de gr\xE1ficos."],"vs/platform/theme/common/iconRegistry":["Identificador de la fuente que se va a usar. Si no se establece, se usa la fuente definida en primer lugar.","Car\xE1cter de fuente asociado a la definici\xF3n del icono.","Icono de la acci\xF3n de cierre en los widgets.","Icono para ir a la ubicaci\xF3n del editor anterior.","Icono para ir a la ubicaci\xF3n del editor siguiente."],"vs/platform/undoRedo/common/undoRedoService":["Se han cerrado los siguientes archivos y se han modificado en el disco: {0}.","Los siguientes archivos se han modificado de forma incompatible: {0}.",'No se pudo deshacer "{0}" en todos los archivos. {1}','No se pudo deshacer "{0}" en todos los archivos. {1}','No se pudo deshacer "{0}" en todos los archivos porque se realizaron cambios en {1}','No se pudo deshacer "{0}" en todos los archivos porque ya hay una operaci\xF3n de deshacer o rehacer en ejecuci\xF3n en {1}','No se pudo deshacer "{0}" en todos los archivos porque se produjo una operaci\xF3n de deshacer o rehacer mientras tanto','\xBFDesea deshacer "{0}" en todos los archivos?',"&&Deshacer en {0} archivos","Deshacer este &&archivo",'No se pudo deshacer "{0}" porque ya hay una operaci\xF3n de deshacer o rehacer en ejecuci\xF3n.','\xBFQuiere deshacer "{0}"?',"&&S\xED","No",'No se pudo rehacer "{0}" en todos los archivos. {1}','No se pudo rehacer "{0}" en todos los archivos. {1}','No se pudo volver a hacer "{0}" en todos los archivos porque se realizaron cambios en {1}','No se pudo rehacer "{0}" en todos los archivos porque ya hay una operaci\xF3n de deshacer o rehacer en ejecuci\xF3n en {1}','No se pudo rehacer "{0}" en todos los archivos porque se produjo una operaci\xF3n de deshacer o rehacer mientras tanto','No se pudo rehacer "{0}" porque ya hay una operaci\xF3n de deshacer o rehacer en ejecuci\xF3n.'],"vs/platform/workspace/common/workspace":["\xC1rea de trabajo de c\xF3digo"]});

//# sourceMappingURL=../../../min-maps/vs/editor/editor.main.nls.es.js.map