# FastGPT 前端项目

基于 React 18 + Vite + TypeScript 的前端应用，已从 Next.js 完全迁移至 React SPA 架构。

## 技术栈

- **框架**: React 18 + TypeScript
- **构建工具**: Vite 5
- **路由**: React Router v6
- **状态管理**: Zustand
- **UI 组件库**: Chakra UI
- **HTTP 客户端**: Axios
- **国际化**: react-i18next
- **包管理器**: pnpm

## 架构迁移说明

项目已完成从 Next.js SSR 架构到 React SPA 架构的迁移：

- ✅ 路由系统：Next.js Router → React Router v6
- ✅ 环境变量：process.env → import.meta.env (VITE_前缀)
- ✅ 构建工具：Next.js → Vite
- ✅ 代理配置：next.config.js → vite.config.ts
- ✅ 样式系统：全局样式导入和优先级修复
- ✅ SVG 处理：vite-plugin-svgr 配置
- ✅ 加密模块：Node.js crypto → crypto-js（浏览器兼容）
- ✅ 并发模式：移除 React Query suspense，使用手动错误处理

## 目录结构

```
.
├── data                      // 迁移自原项目:/projects/app/data
├── public                    // 迁移自原项目:/projects/app/public
├── src                       // 源代码目录
│   ├── components            // 迁移自原项目:/projects/app/src/components
│   ├── global                // 迁移自原项目:/projects/app/src/global
│   ├── packages              // 公共包
│   │   ├── common、components、context、core等   // 迁移自原项目:/packages/web目录
│   │   └── global            // 迁移自原项目:/packages/global
│   ├── pageComponents        // 迁移自原项目:/projects/app/src/pageComponents
│   ├── pages                 // 迁移自原项目:/projects/app/src/pages
│   ├── routes                // React Router 路由配置
│   ├── service               // 迁移自原项目:/projects/app/src/service
│   ├── types                 // 迁移自原项目:/projects/app/src/types
│   ├── web                   // 迁移自原项目:/projects/app/src/web
│   ├── styles                // 全局样式文件
│   ├── i18n                  // 国际化配置
│   ├── App.tsx               // 应用根组件
│   └── main.tsx              // 应用入口文件
├── vite.config.ts            // Vite 配置文件
├── package.json              // 依赖配置
└── .env.development                // 环境变量配置
```

## 启动说明

**必要条件：** Node.js 需要为 v20 版本

1. 进入根目录，使用 pnpm 安装依赖：

```bash
pnpm i --registry=https://registry.npmmirror.com
```

2. 配置环境变量，创建 `.env.development` 文件并配置以下变量：

```properties
# 开发环境标识
NODE_ENV=development

# 后端服务地址
VITE_BACKEND_URL=http://**************:3000
VITE_LAF_ENV=http://**************:3002
VITE_FASTGPT_PRO_URL=http://**************:3003
```

**注意：** Vite 环境变量必须以 `VITE_` 开头才能在客户端代码中访问。

3. 如果需要调整代理配置，请编辑 `vite.config.ts` 文件中的 server.proxy 配置：

```typescript
server: {
  port: 3000,
  proxy: {
    '/api': {
      target: env.VITE_BACKEND_URL || 'http://localhost:3001',
      changeOrigin: true,
    },
    '/aiproxy': {
      target: env.VITE_BACKEND_URL || 'http://localhost:3001',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/aiproxy/, '/api/aiproxy/api'),
    },
    '/lafApi': {
      target: env.VITE_LAF_ENV || 'http://localhost:3002',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/lafApi/, ''),
    },
    '/proApi': {
      target: env.VITE_FASTGPT_PRO_URL || 'http://localhost:3003',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/proApi/, '/api'),
    }
  }
}
```

4. 启动开发服务器：

```bash
pnpm dev
```

访问地址：http://localhost:3000




## 代理工作原理

Vite 的代理机制基于 http-proxy-middleware，工作原理如下：

1. 前端代码发起请求：`/api/common/system/getInitData`
2. Vite 开发服务器拦截请求并代理到：`http://**************:3000/api/common/system/getInitData`
3. 后端响应返回给前端，避免跨域问题

**关键点**：
- 前端请求使用相对路径（如 `/api/xxx`、`/aiproxy/xxx`、`/lafApi/xxx`、`/proApi/xxx`）
- Vite 开发服务器作为代理转发请求
- 浏览器看到的是同源请求，不会触发 CORS
- 支持路径重写（rewrite）功能，可以修改代理路径


## 常见问题

**Q: 还是有 CORS 错误？**
A:
1. 确认 `.env.development` 中的环境变量是否正确配置
2. 确认环境变量名是否以 `VITE_` 开头
3. 删除 `node_modules/.vite` 缓存目录并重启：
   ```bash
   rm -rf node_modules/.vite
   pnpm dev
   ```
4. 清除浏览器缓存

**Q: 请求 404 错误？**
A:
1. 确认后端服务正常运行
2. 确认后端 API 路径结构
3. 检查 `vite.config.ts` 中的代理配置
4. 确认请求路径是否匹配代理规则（/api、/aiproxy、/lafApi、/proApi）

**Q: 环境变量读取不到？**
A:
1. 确认环境变量名以 `VITE_` 开头
2. 重启开发服务器
3. 检查 `.env.development` 文件是否在项目根目录

**Q: 如何确认代理是否生效？**
A: 在浏览器网络面板中，请求的 URL 应该是：
- ✅ 正确：`http://localhost:3000/api/common/system/getInitData`
- ❌ 错误：`http://**************:3000/api/common/system/getInitData`

**Q: 构建部署后如何处理代理？**
A: 生产环境需要在 Nginx 或其他 Web 服务器中配置代理，或者修改 API 请求地址为实际后端地址。




