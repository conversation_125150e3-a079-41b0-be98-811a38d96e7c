import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import svgr from 'vite-plugin-svgr'
import path from 'path'
import fs from 'fs'
import { Agent } from 'node:http'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 支持通过环境变量指定配置文件
  const envFile = process.env.VITE_ENV_FILE;
  let env;

  if (envFile) {
    // 手动加载指定的环境文件
    const envPath = path.resolve(process.cwd(), envFile);

    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf-8');
      env = {};

      // 解析环境文件
      envContent.split('\n').forEach(line => {
        const trimmedLine = line.trim();
        if (trimmedLine && !trimmedLine.startsWith('#')) {
          const [key, ...valueParts] = trimmedLine.split('=');
          if (key && valueParts.length > 0) {
            const value = valueParts.join('=').trim();
            env[key.trim()] = value;
          }
        }
      });

      console.log(`✅ React应用加载环境配置: ${envFile}`);
    } else {
      console.warn(`⚠️ 环境文件不存在: ${envFile}，使用默认配置`);
      env = loadEnv(mode, process.cwd(), '');
    }
  } else {
    // 使用默认的环境加载方式
    env = loadEnv(mode, process.cwd(), '');
  }
  
  return {
    base: env.VITE_BASE,
    plugins: [
      react({
        jsxRuntime: 'automatic',
      }),
      svgr({
        include: "**/*.svg",
      }),
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    define: {
      global: 'globalThis',
    },
    optimizeDeps: {
      include: ['react', 'react-dom'],
      exclude: ['jschardet']
    },
    server: {
      port: 5100,
      proxy: {
        '/frontend': {
          target: env.VITE_BACKEND_URL,
          changeOrigin: true,
          agent: new Agent({ keepAlive: true, keepAliveMsecs: 5000000 }),
          timeout: 5000000,
          proxyTimeout: 5000000
        },
        '/mind': {
          target: env.VITE_BACKEND_URL,
          changeOrigin: true,
          agent: new Agent({ keepAlive: true, keepAliveMsecs: 50000 }),
        },
        '/lafApi': {
          target: env.VITE_LAF_ENV,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/lafApi/, ''),
          agent: new Agent({ keepAlive: true, keepAliveMsecs: 50000 }),
        }
      }
    },
    build: {
      outDir: 'dist',
      sourcemap: false,
      minify: 'esbuild',
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
            router: ['react-router-dom'],
            ui: ['@chakra-ui/react', '@emotion/react', '@emotion/styled', 'framer-motion'],
            charts: ['echarts', 'recharts'],
            utils: ['lodash', 'dayjs', 'axios'],
            progress: ['nprogress']
          },
          // 确保chunk文件名的一致性
          chunkFileNames: 'assets/[name]-[hash].js',
          entryFileNames: 'assets/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]'
        }
      },
      // 增加chunk大小限制警告阈值
      chunkSizeWarningLimit: 1000,
      // 优化生产环境配置以减少DOM操作问题
      target: 'esnext',
      cssCodeSplit: true,
      // 确保模块预加载
      modulePreload: {
        polyfill: true
      },
      // 添加额外的优化选项
      assetsInlineLimit: 4096,
      // 确保正确的模块格式
      lib: undefined,
      // 优化依赖预构建
      commonjsOptions: {
        include: [/node_modules/],
        transformMixedEsModules: true
      }
    }
  }
}) 