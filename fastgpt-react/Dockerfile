# --------- 构建阶段 -----------
FROM node:20.14.0-alpine AS builder
WORKDIR /app

ARG proxy
ARG base_url

# 如果有代理，设置镜像源
RUN [ -z "$proxy" ] || sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories
RUN apk add --no-cache libc6-compat && npm install -g pnpm@9.4.0

# 复制依赖文件
COPY package.json ./
COPY pnpm-lock.yaml* ./

# 如果没有锁定文件，生成一个
RUN [ -f pnpm-lock.yaml ] || echo "Warning: No lockfile found, will generate during install"

# 安装依赖
#RUN if [ -z "$proxy" ]; then \
#        pnpm install --no-frozen-lockfile; \
#    else \
#        pnpm install --no-frozen-lockfile --registry=http://**************:8088/repository/sinitek-npm; \
#    fi
RUN pnpm install --no-frozen-lockfile --registry=http://**************:8088/repository/sinitek-npm

# 复制源代码和配置文件
COPY . .

# 设置环境变量
ENV NODE_OPTIONS="--max-old-space-size=4096"
ENV NEXT_PUBLIC_BASE_URL=$base_url
ENV NODE_ENV=production

# 添加构建时需要的 NEXT_PUBLIC 环境变量
ARG NEXT_PUBLIC_API_BASE_URL
ARG NEXT_PUBLIC_FRONTEND_PREFIX
ARG NEXT_PUBLIC_AIPROXY_BASE_URL

ENV NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL:-/gpt/api}
ENV NEXT_PUBLIC_FRONTEND_PREFIX=${NEXT_PUBLIC_FRONTEND_PREFIX:-/gpt}
ENV NEXT_PUBLIC_AIPROXY_BASE_URL=${NEXT_PUBLIC_AIPROXY_BASE_URL:-/aiproxy}

# 构建应用
RUN npm run build

# --------- 运行环境 -----------
FROM node:20.14.0-alpine AS runner
WORKDIR /app

ARG proxy
ARG base_url

# 创建用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 设置镜像源和安装基础包
RUN [ -z "$proxy" ] || sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories
RUN apk add --no-cache curl

# 复制构建产物
COPY --from=builder /app/public ./public
COPY --from=builder /app/next.config.js ./
COPY --from=builder /app/next-i18next.config.js ./
COPY --from=builder /app/package.json ./

# 复制Next.js构建输出
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# 复制配置文件
COPY --from=builder /app/data ./data
RUN chown -R nextjs:nodejs ./data

# 设置环境变量（可通过 docker run -e 或 docker-compose 覆盖）
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000

# 后端 API 接口前缀
ENV NEXT_PUBLIC_BASE_URL=$base_url

# 请求后端API接口时，拼接的基础前缀
ENV NEXT_PUBLIC_API_BASE_URL="/gpt/api"
# 前端页面路由前缀
ENV NEXT_PUBLIC_FRONTEND_PREFIX="/gpt"
ENV NEXT_PUBLIC_AIPROXY_BASE_URL="/aiproxy"
ENV FASTGPT_PRO_URL=""

# 后端API地址（生产环境下不使用代理，直接连接后端）
ENV BACKEND_URL=""
ENV JAVA_BACKEND_URL=""


# AI 服务配置
ENV ONEAPI_URL=""
ENV CHAT_API_KEY=""
ENV AIPROXY_API_ENDPOINT=""
ENV AIPROXY_API_TOKEN="aiproxy"

# 其他服务配置
ENV LAF_ENV=""
ENV DEFAULT_ROOT_PSW="123456"
ENV SHOW_COUPON="false"

# 配置文件路径
ENV CONFIG_JSON_PATH="/app/data"

EXPOSE 3000

USER nextjs

# 启动应用
CMD ["node", "server.js"]