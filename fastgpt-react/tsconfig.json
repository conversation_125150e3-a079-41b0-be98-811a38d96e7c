{"compilerOptions": {"target": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noImplicitAny": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@fastgpt/service/*": ["./src/types/service.d.ts"], "@fastgpt/service/core/ai/type": ["./src/types/service.d.ts"], "@fastgpt/service/core/app/plugin/type": ["./src/types/service.d.ts"], "@fastgpt/service/support/user/inform/type": ["./src/types/service.d.ts"], "@fastgpt/service/support/user/team/invitationLink/type": ["./src/types/service.d.ts"], "@fastgpt/service/support/outLink/schema": ["./src/types/service.d.ts"], "@fastgpt/service/common/system/log": ["./src/types/service.d.ts"], "@fastgpt/service/support/permission/org/controllers": ["./src/types/service.d.ts"], "@/pages/api/*": ["./src/types/api.d.ts"]}}, "include": ["src/**/*", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "dist"]}