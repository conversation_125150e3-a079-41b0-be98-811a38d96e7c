import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Box, Flex, Input, <PERSON><PERSON>Footer, But<PERSON> } from '@chakra-ui/react';
import MyModal from '@/packages/components/common/MyModal';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { useRequest2 } from '@/packages/hooks/useRequest';
import type { OpenaiAccountType } from '@/packages/global/support/user/team/type';
import { useUserStore } from '@/web/support/user/useUserStore';
import { putUpdateTeam } from '@/web/support/user/team/api';

const OpenAIAccountModal = ({
  defaultData,
  onClose
}: {
  defaultData?: OpenaiAccountType;
  onClose: () => void;
}) => {
  const { t } = useTranslation();
  const { userInfo, initUserInfo } = useUserStore();
  const { register, handleSubmit } = useForm({
    defaultValues: defaultData
  });

  const { runAsync: onSubmit, loading } = useRequest2(
    async (data: OpenaiAccountType) => {
      if (!userInfo?.team.teamId) return;
      return putUpdateTeam({
        openaiAccount: data
      });
    },
    {
      onSuccess: () => {
        initUserInfo();
        onClose();
      },
      successToast: t('common:update_success'),
      errorToast: t('common:update_failed')
    }
  );

  return (
    <MyModal
      isOpen
      onClose={onClose}
      iconSrc="common/openai"
      title={t('account_thirdParty:openai_account_configuration')}
    >
      <ModalBody>
        <Box fontSize={'sm'} color={'myGray.500'}>
          {t('account_thirdParty:open_api_notice')}
        </Box>
        <Flex alignItems={'center'} mt={5}>
          <Box flex={'0 0 65px'}>API Key:</Box>
          <Input flex={1} {...register('key')}></Input>
        </Flex>
        <Flex alignItems={'center'} mt={5}>
          <Box flex={'0 0 65px'}>BaseUrl:</Box>
          <Input
            flex={1}
            {...register('baseUrl')}
            placeholder={t('account_thirdParty:request_address_notice')}
          />
        </Flex>
      </ModalBody>
      <ModalFooter>
        <Button mr={3} variant={'whiteBase'} onClick={onClose}>
          {t('common:Cancel')}
        </Button>
        <Button isLoading={loading} onClick={handleSubmit(onSubmit)}>
          {t('common:Confirm')}
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default OpenAIAccountModal;
