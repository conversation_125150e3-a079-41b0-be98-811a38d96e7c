import React, { useMemo } from 'react';
import { Box, Flex } from '@chakra-ui/react';
import { lazy } from 'react';
import { ImportDataSourceEnum } from '@/packages/global/core/dataset/constants';
import { useContextSelector } from 'use-context-selector';
import DatasetImportContextProvider, { DatasetImportContext } from './Context';

const FileLocal = lazy(() => import('./diffSource/FileLocal'));
const FileLink = lazy(() => import('./diffSource/FileLink'));
const FileCustomText = lazy(() => import('./diffSource/FileCustomText'));
const ExternalFileCollection = lazy(() => import('./diffSource/ExternalFile'));
const APIDatasetCollection = lazy(() => import('./diffSource/APIDataset'));
const ReTraining = lazy(() => import('./diffSource/ReTraining'));
const ImageDataset = lazy(() => import('./diffSource/ImageDataset'));

const ImportDataset = () => {
  const importSource = useContextSelector(DatasetImportContext, (v) => v.importSource);

  const ImportComponent = useMemo(() => {
    if (importSource === ImportDataSourceEnum.reTraining) return ReTraining;
    if (importSource === ImportDataSourceEnum.fileLocal) return FileLocal;
    if (importSource === ImportDataSourceEnum.fileLink) return FileLink;
    if (importSource === ImportDataSourceEnum.fileCustom) return FileCustomText;
    if (importSource === ImportDataSourceEnum.externalFile) return ExternalFileCollection;
    if (importSource === ImportDataSourceEnum.apiDataset) return APIDatasetCollection;
    if (importSource === ImportDataSourceEnum.imageDataset) return ImageDataset;
    return null;
  }, [importSource]);

  return ImportComponent ? (
    <Box flex={'1 0 0'} overflow={'auto'}>
      <ImportComponent />
    </Box>
  ) : null;
};

const Render = () => {
  return (
    <Flex
      flexDirection={'column'}
      bg={'white'}
      h={'100%'}
      px={[2, 9]}
      py={[2, 5]}
      borderRadius={'md'}
    >
      <DatasetImportContextProvider>
        <ImportDataset />
      </DatasetImportContextProvider>
    </Flex>
  );
};

export default React.memo(Render);
