import React, { useState } from 'react';
import MyModal from '@/packages/components/common/MyModal';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer, <PERSON><PERSON> } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import LeftRadio from '@/packages/components/common/Radio/LeftRadio';
import { useNavigate, useLocation, useSearchParams } from "react-router-dom";
import { TabEnum } from '../../../../../pages/dataset/detail';
import { ImportDataSourceEnum } from '@/packages/global/core/dataset/constants';

const FileModeSelector = ({ onClose }: { onClose: () => void }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [value, setValue] = useState<ImportDataSourceEnum>(ImportDataSourceEnum.fileLocal);

  return (
    <MyModal
      isOpen
      onClose={onClose}
      iconSrc="modal/selectSource"
      title={t('common:core.dataset.import.Select source')}
      w={'600px'}
    >
      <ModalBody px={6} py={4}>
        <LeftRadio
          list={[
            {
              title: t('common:core.dataset.import.Local file'),
              desc: t('common:core.dataset.import.Local file desc'),
              value: ImportDataSourceEnum.fileLocal
            },
            {
              title: t('common:core.dataset.import.Web link'),
              desc: t('common:core.dataset.import.Web link desc'),
              value: ImportDataSourceEnum.fileLink
            },
            {
              title: t('common:core.dataset.import.Custom text'),
              desc: t('common:core.dataset.import.Custom text desc'),
              value: ImportDataSourceEnum.fileCustom
            }
          ]}
          value={value}
          onChange={setValue}
        />
      </ModalBody>
      <ModalFooter>
        <Button
          onClick={() => {
            const newSearchParams = new URLSearchParams(searchParams);
            newSearchParams.set('source', value);
            newSearchParams.set('currentTab', TabEnum.import);
            navigate(`/dataset/detail?${newSearchParams.toString()}`);
          }}
        >
          {t('common:Confirm')}
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default FileModeSelector;
