import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ModalBody, Button, Flex, Box } from '@chakra-ui/react';
import MyModal from '@/packages/components/common/MyModal/index';
import { useTranslation } from 'react-i18next';
import { useRequest2 } from '@/packages/hooks/useRequest';
import { useForm } from 'react-hook-form';
import { useToast } from '@/packages/hooks/useToast';
import ApiDatasetForm from '@/pageComponents/dataset/ApiDatasetForm';
import { useContextSelector } from 'use-context-selector';
import { DatasetPageContext } from '@/web/core/dataset/context/datasetPageContext';
import { getDocPath } from '@/web/common/system/doc';
import MyIcon from '@/packages/components/common/Icon';
import type { ApiDatasetServerType } from '@/packages/global/core/dataset/apiDataset/type';
import { DatasetTypeMap } from '@/packages/global/core/dataset/constants';

export type EditAPIDatasetInfoFormType = {
  id: string;
  apiDatasetServer?: ApiDatasetServerType;
};

const EditAPIDatasetInfoModal = ({
  onClose,
  onEdit,
  title,
  ...defaultForm
}: EditAPIDatasetInfoFormType & {
  title: string;
  onClose: () => void;
  onEdit: (data: EditAPIDatasetInfoFormType) => any;
}) => {
  const { t } = useTranslation();
  const { toast } = useToast();

  const datasetDetail = useContextSelector(DatasetPageContext, (v) => v.datasetDetail);
  const type = datasetDetail.type;

  const form = useForm<EditAPIDatasetInfoFormType>({
    defaultValues: defaultForm
  });

  const { runAsync: onSave, loading } = useRequest2(
    (data: EditAPIDatasetInfoFormType) => onEdit(data),
    {
      onSuccess: (res) => {
        toast({
          title: t('common:update_success'),
          status: 'success'
        });
        onClose();
      }
    }
  );

  return (
    <MyModal isOpen onClose={onClose} w={'450px'} iconSrc="modal/edit" title={title}>
      <ModalBody>
        {DatasetTypeMap[type]?.courseUrl && (
          <Flex alignItems={'center'} justifyContent={'space-between'}>
            <Box color={'myGray.900'} fontSize={'sm'} fontWeight={500}>
              {t('dataset:apidataset_configuration')}
            </Box>
            <Flex
              alignItems={'center'}
              justifyContent={'flex-end'}
              color={'primary.600'}
              fontSize={'sm'}
              cursor={'pointer'}
              onClick={() => window.open(getDocPath(DatasetTypeMap[type].courseUrl!), '_blank')}
            >
              <MyIcon name={'book'} w={4} mr={0.5} />
              {t('common:Instructions')}
            </Flex>
          </Flex>
        )}
        {/* @ts-ignore */}
        <ApiDatasetForm datasetId={datasetDetail._id} type={type} form={form} />
      </ModalBody>
      <ModalFooter>
        <Button isLoading={loading} onClick={form.handleSubmit(onSave)} px={6}>
          {t('common:Confirm')}
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default EditAPIDatasetInfoModal;
