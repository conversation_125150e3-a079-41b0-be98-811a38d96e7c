import React from 'react';
import <PERSON>act<PERSON><PERSON>, { type NodeProps, SelectionMode } from 'reactflow';
import { Box, IconButton, useDisclosure } from '@chakra-ui/react';
import { EDGE_TYPE, FlowNodeTypeEnum } from '@/packages/global/core/workflow/node/constant';

import { lazy } from 'react';

import ButtonEdge from './components/ButtonEdge';
import NodeTemplatesModal from './NodeTemplatesModal';

import 'reactflow/dist/style.css';
import { type FlowNodeItemType } from '@/packages/global/core/workflow/type/node.d';
import { connectionLineStyle, defaultEdgeOptions, maxZoom, minZoom } from '../constants';
import { useContextSelector } from 'use-context-selector';
import { useWorkflow } from './hooks/useWorkflow';
import HelperLines from './components/HelperLines';
import FlowController from './components/FlowController';
import ContextMenu from './components/ContextMenu';
import { WorkflowNodeEdgeContext, WorkflowInitContext } from '../context/workflowInitContext';
import { WorkflowEventContext } from '../context/workflowEventContext';
import NodeTemplatesPopover from './NodeTemplatesPopover';
import SearchButton from '../../Workflow/components/SearchButton';
import MyIcon from '@/packages/components/common/Icon';

const NodeSimple = lazy(() => import('./nodes/NodeSimple'));
const nodeTypes: Record<FlowNodeTypeEnum, any> = {
  [FlowNodeTypeEnum.emptyNode]: NodeSimple,
  [FlowNodeTypeEnum.globalVariable]: NodeSimple,
  [FlowNodeTypeEnum.textEditor]: NodeSimple,
  [FlowNodeTypeEnum.customFeedback]: NodeSimple,
  [FlowNodeTypeEnum.systemConfig]: lazy(() => import('./nodes/NodeSystemConfig')),
  [FlowNodeTypeEnum.pluginConfig]: lazy(() => import('./nodes/NodePluginIO/NodePluginConfig')),
  [FlowNodeTypeEnum.workflowStart]: lazy(() => import('./nodes/NodeWorkflowStart')),
  [FlowNodeTypeEnum.chatNode]: NodeSimple,
  [FlowNodeTypeEnum.readFiles]: NodeSimple,
  [FlowNodeTypeEnum.datasetSearchNode]: NodeSimple,
  [FlowNodeTypeEnum.datasetConcatNode]: lazy(() => import('./nodes/NodeDatasetConcat')),
  [FlowNodeTypeEnum.answerNode]: lazy(() => import('./nodes/NodeAnswer')),
  [FlowNodeTypeEnum.classifyQuestion]: lazy(() => import('./nodes/NodeCQNode')),
  [FlowNodeTypeEnum.contentExtract]: lazy(() => import('./nodes/NodeExtract')),
  [FlowNodeTypeEnum.httpRequest468]: lazy(() => import('./nodes/NodeHttp')),
  [FlowNodeTypeEnum.runApp]: NodeSimple,
  [FlowNodeTypeEnum.appModule]: NodeSimple,
  [FlowNodeTypeEnum.pluginInput]: lazy(() => import('./nodes/NodePluginIO/PluginInput')),
  [FlowNodeTypeEnum.pluginOutput]: lazy(() => import('./nodes/NodePluginIO/PluginOutput')),
  [FlowNodeTypeEnum.pluginModule]: NodeSimple,
  [FlowNodeTypeEnum.queryExtension]: NodeSimple,
  [FlowNodeTypeEnum.tools]: lazy(() => import('./nodes/NodeTools')),
  [FlowNodeTypeEnum.stopTool]: (data: NodeProps<FlowNodeItemType>) => (
    <NodeSimple {...data} minW={'100px'} maxW={'300px'} />
  ),
  [FlowNodeTypeEnum.tool]: lazy(() => import('./nodes/NodeTool')),
  [FlowNodeTypeEnum.toolSet]: lazy(() => import('./nodes/NodeToolSet')),
  [FlowNodeTypeEnum.toolParams]: lazy(() => import('./nodes/NodeToolParams')),
  [FlowNodeTypeEnum.lafModule]: lazy(() => import('./nodes/NodeLaf')),
  [FlowNodeTypeEnum.ifElseNode]: lazy(() => import('./nodes/NodeIfElse')),
  [FlowNodeTypeEnum.variableUpdate]: lazy(() => import('./nodes/NodeVariableUpdate')),
  [FlowNodeTypeEnum.code]: lazy(() => import('./nodes/NodeCode')),
  [FlowNodeTypeEnum.userSelect]: lazy(() => import('./nodes/NodeUserSelect')),
  [FlowNodeTypeEnum.loop]: lazy(() => import('./nodes/Loop/NodeLoop')),
  [FlowNodeTypeEnum.loopStart]: lazy(() => import('./nodes/Loop/NodeLoopStart')),
  [FlowNodeTypeEnum.loopEnd]: lazy(() => import('./nodes/Loop/NodeLoopEnd')),
  [FlowNodeTypeEnum.formInput]: lazy(() => import('./nodes/NodeFormInput')),
  [FlowNodeTypeEnum.comment]: lazy(() => import('./nodes/NodeComment'))
};
const edgeTypes = {
  [EDGE_TYPE]: ButtonEdge
};

const Workflow = () => {
  const nodes = useContextSelector(WorkflowInitContext, (v) => v.nodes);
  const edges = useContextSelector(WorkflowNodeEdgeContext, (v) => v.edges);
  const reactFlowWrapper = useContextSelector(WorkflowEventContext, (v) => v.reactFlowWrapper);
  const workflowControlMode = useContextSelector(
    WorkflowEventContext,
    (v) => v.workflowControlMode
  );
  const menu = useContextSelector(WorkflowEventContext, (v) => v.menu);

  const {
    handleNodesChange,
    handleEdgeChange,
    onConnectStart,
    onConnectEnd,
    customOnConnect,
    onEdgeMouseEnter,
    onEdgeMouseLeave,
    helperLineHorizontal,
    helperLineVertical,
    onNodeDragStop,
    onPaneContextMenu,
    onPaneClick
  } = useWorkflow();

  const {
    isOpen: isOpenTemplate,
    onOpen: onOpenTemplate,
    onClose: onCloseTemplate
  } = useDisclosure();

  return (
    <>
      <Box
        flex={'1 0 0'}
        h={0}
        w={'100%'}
        position={'relative'}
        onContextMenu={(e) => {
          e.preventDefault();
          return false;
        }}
      >
        {/* open module template */}
        <>
          <IconButton
            position={'absolute'}
            top={6}
            left={6}
            size={'mdSquare'}
            borderRadius={'50%'}
            icon={<MyIcon name="common/addLight" w={'26px'} />}
            transition={'0.2s ease'}
            aria-label={''}
            zIndex={1}
            boxShadow={
              '0px 4px 10px 0px rgba(19, 51, 107, 0.20), 0px 0px 1px 0px rgba(19, 51, 107, 0.50)'
            }
            onClick={() => {
              isOpenTemplate ? onCloseTemplate() : onOpenTemplate();
            }}
          />
          <SearchButton />
          <NodeTemplatesModal isOpen={isOpenTemplate} onClose={onCloseTemplate} />
          <NodeTemplatesPopover />
        </>

        <ReactFlow
          ref={reactFlowWrapper}
          fitView
          nodes={nodes}
          edges={edges}
          minZoom={minZoom}
          maxZoom={maxZoom}
          defaultEdgeOptions={defaultEdgeOptions}
          elevateEdgesOnSelect
          connectionLineStyle={connectionLineStyle}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          connectionRadius={50}
          onNodesChange={handleNodesChange}
          onEdgesChange={handleEdgeChange}
          onConnect={customOnConnect}
          onConnectStart={onConnectStart}
          onConnectEnd={onConnectEnd}
          onEdgeMouseEnter={onEdgeMouseEnter}
          onEdgeMouseLeave={onEdgeMouseLeave}
          panOnScrollSpeed={2}
          onPaneContextMenu={onPaneContextMenu}
          onPaneClick={onPaneClick}
          {...(workflowControlMode === 'select'
            ? {
                selectionMode: SelectionMode.Full,
                selectNodesOnDrag: false,
                selectionOnDrag: true,
                selectionKeyCode: null,
                panOnDrag: false,
                panOnScroll: true
              }
            : {})}
          onNodeDragStop={onNodeDragStop}
        >
          {!!menu && <ContextMenu />}
          <FlowController />
          <HelperLines horizontal={helperLineHorizontal} vertical={helperLineVertical} />
        </ReactFlow>
      </Box>
    </>
  );
};

export default React.memo(Workflow);
