import React, { useMemo } from 'react';
import type { FlowNodeInputItemType } from '@/packages/global/core/workflow/type/io.d';
import { Box } from '@chakra-ui/react';
import { FlowNodeInputTypeEnum } from '@/packages/global/core/workflow/node/constant';
import { lazy } from 'react';

import InputLabel from './Label';
import type { RenderInputProps } from './type';
import { useSystemStore } from '@/web/common/system/useSystemStore';

const RenderList: Record<
  FlowNodeInputTypeEnum,
  | {
      Component: React.ComponentType<RenderInputProps>;
      LableRightComponent?: React.ComponentType<RenderInputProps>;
    }
  | undefined
> = {
  [FlowNodeInputTypeEnum.reference]: {
    Component: lazy(() => import('./templates/Reference'))
  },
  [FlowNodeInputTypeEnum.fileSelect]: {
    Component: lazy(() => import('./templates/Reference'))
  },
  [FlowNodeInputTypeEnum.select]: {
    Component: lazy(() => import('./templates/Select'))
  },
  [FlowNodeInputTypeEnum.multipleSelect]: {
    Component: lazy(() => import('./templates/SelectMulti'))
  },
  [FlowNodeInputTypeEnum.numberInput]: {
    Component: lazy(() => import('./templates/NumberInput'))
  },
  [FlowNodeInputTypeEnum.switch]: {
    Component: lazy(() => import('./templates/Switch'))
  },
  [FlowNodeInputTypeEnum.selectApp]: {
    Component: lazy(() => import('./templates/SelectApp'))
  },
  [FlowNodeInputTypeEnum.selectLLMModel]: {
    Component: lazy(() => import('./templates/SelectLLMModel'))
  },
  [FlowNodeInputTypeEnum.settingLLMModel]: {
    Component: lazy(() => import('./templates/SettingLLMModel'))
  },
  [FlowNodeInputTypeEnum.selectDataset]: {
    Component: lazy(() =>
      import('./templates/SelectDataset').then((mod) => ({ default: mod.SelectDatasetRender }))
    ),
    LableRightComponent: lazy(() =>
      import('./templates/SelectDataset').then((mod) => ({ default: mod.SwitchAuthTmb }))
    )
  },
  [FlowNodeInputTypeEnum.selectDatasetParamsModal]: {
    Component: lazy(() => import('./templates/SelectDatasetParams'))
  },
  [FlowNodeInputTypeEnum.addInputParam]: {
    Component: lazy(() => import('./templates/DynamicInputs/index'))
  },
  [FlowNodeInputTypeEnum.JSONEditor]: {
    Component: lazy(() => import('./templates/JsonEditor'))
  },
  [FlowNodeInputTypeEnum.settingDatasetQuotePrompt]: {
    Component: lazy(() => import('./templates/SettingQuotePrompt'))
  },
  [FlowNodeInputTypeEnum.input]: {
    Component: lazy(() => import('./templates/TextInput'))
  },
  [FlowNodeInputTypeEnum.textarea]: {
    Component: lazy(() => import('./templates/Textarea')),
    LableRightComponent: lazy(() =>
      import('./templates/Textarea').then((mod) => ({ default: mod.TextareaRightComponent }))
    )
  },

  [FlowNodeInputTypeEnum.customVariable]: undefined,
  [FlowNodeInputTypeEnum.hidden]: undefined,
  [FlowNodeInputTypeEnum.custom]: undefined
};

const hideLabelTypeList = [FlowNodeInputTypeEnum.addInputParam];

type Props = {
  flowInputList: FlowNodeInputItemType[];
  nodeId: string;
  CustomComponent?: Record<string, (e: FlowNodeInputItemType) => React.ReactNode>;
  mb?: number;
};
const RenderInput = ({ flowInputList, nodeId, CustomComponent, mb = 5 }: Props) => {
  const { feConfigs } = useSystemStore();

  const filterProInputs = useMemo(() => {
    return flowInputList.filter((input) => {
      if (input.isPro && !feConfigs?.isPlus) return false;
      return true;
    });
  }, [feConfigs?.isPlus, flowInputList]);

  const filterInputs = useMemo(() => {
    return filterProInputs.filter((input) => {
      const renderType = input.renderTypeList?.[input.selectedTypeIndex || 0];
      const isDynamic = !!input.canEdit;

      if (renderType === FlowNodeInputTypeEnum.hidden || isDynamic) return false;

      return true;
    });
  }, [filterProInputs]);

  return (
    <>
      {filterInputs.map((input) => {
        const renderType = input.renderTypeList?.[input.selectedTypeIndex || 0];

        const RenderComponent = (() => {
          if (renderType === FlowNodeInputTypeEnum.custom && CustomComponent?.[input.key]) {
            return {
              Component: <>{CustomComponent?.[input.key]({ ...input })}</>
            };
          }

          const RenderItem = RenderList[renderType];

          if (!RenderItem) return null;

          return {
            Component: (
              <RenderItem.Component inputs={filterProInputs} item={input} nodeId={nodeId} />
            ),
            LableRightComponent: RenderItem.LableRightComponent ? (
              <RenderItem.LableRightComponent
                inputs={filterProInputs}
                item={input}
                nodeId={nodeId}
              />
            ) : undefined
          };
        })();

        return (
          <Box key={input.key} _notLast={{ mb }} position={'relative'}>
            {!!input.label && !hideLabelTypeList.includes(renderType) && (
              <InputLabel
                nodeId={nodeId}
                input={input}
                RightComponent={RenderComponent?.LableRightComponent}
              />
            )}
            {!!RenderComponent && (
              <Box mt={2} className={'nodrag'}>
                {RenderComponent.Component}
              </Box>
            )}
          </Box>
        );
      })}
    </>
  );
};

export default React.memo(RenderInput);
