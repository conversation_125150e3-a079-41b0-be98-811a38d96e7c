import React, { use<PERSON>em<PERSON>, useState } from 'react';
import {
  Box,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Table<PERSON>ontainer,
  <PERSON>lex
} from '@chakra-ui/react';
import { type NodeProps } from 'reactflow';
import { type FlowNodeItemType } from '@/packages/global/core/workflow/type/node.d';
import { useTranslation } from 'react-i18next';
import NodeCard from '../render/NodeCard';
import Container from '../../components/Container';
import { AddIcon } from '@chakra-ui/icons';
import RenderInput from '../render/RenderInput';
import type { ContextExtractAgentItemType } from '@/packages/global/core/workflow/template/system/contextExtract/type';
import RenderOutput from '../render/RenderOutput';
import MyIcon from '@/packages/components/common/Icon';
import ExtractFieldModal, { defaultField } from './ExtractFieldModal';
import { NodeInputKeyEnum } from '@/packages/global/core/workflow/constants';
import { FlowNodeOutputTypeEnum } from '@/packages/global/core/workflow/node/constant';
import { WorkflowIOValueTypeEnum } from '@/packages/global/core/workflow/constants';
import RenderToolInput from '../render/RenderToolInput';
import {
  type FlowNodeInputItemType,
  type FlowNodeOutputItemType
} from '@/packages/global/core/workflow/type/io.d';
import { getNanoid } from '@/packages/global/common/string/tools';
import IOTitle from '../../components/IOTitle';
import { useContextSelector } from 'use-context-selector';
import { WorkflowContext } from '../../../context';
import MyIconButton from '@/packages/components/common/Icon/button';
import MyTooltip from '@/packages/components/common/MyTooltip';

const NodeExtract = ({ data, selected }: NodeProps<FlowNodeItemType>) => {
  const { inputs, outputs, nodeId } = data;

  const { t } = useTranslation();
  const onChangeNode = useContextSelector(WorkflowContext, (v) => v.onChangeNode);

  const splitToolInputs = useContextSelector(WorkflowContext, (ctx) => ctx.splitToolInputs);
  const { isTool, commonInputs } = splitToolInputs(inputs, nodeId);
  const [editExtractFiled, setEditExtractField] = useState<ContextExtractAgentItemType>();

  const CustomComponent = useMemo(
    () => ({
      [NodeInputKeyEnum.extractKeys]: ({
        value: extractKeys = [],
        ...props
      }: Omit<FlowNodeInputItemType, 'value'> & {
        value?: ContextExtractAgentItemType[];
      }) => (
        <Box mt={-2}>
          <Flex alignItems={'center'}>
            <Box flex={'1 0 0'} fontSize={'sm'} fontWeight={'medium'} color={'myGray.600'}>
              {t('common:core.module.extract.Target field')}
            </Box>
            <Button
              size={'sm'}
              variant={'grayGhost'}
              px={2}
              color={'myGray.600'}
              leftIcon={<AddIcon fontSize={'10px'} />}
              onClick={() => setEditExtractField(defaultField)}
            >
              {t('common:core.module.extract.Add field')}
            </Button>
          </Flex>

          <TableContainer borderRadius={'md'} overflow={'auto'} borderWidth={'1px'} mt={2}>
            <Table variant={'workflow'}>
              <Thead>
                <Tr>
                  <Th>{t('common:item_name')}</Th>
                  <Th>{t('common:item_description')}</Th>
                  <Th>{t('common:required')}</Th>
                  <Th></Th>
                </Tr>
              </Thead>
              <Tbody>
                {extractKeys.map((item, index) => (
                  <Tr key={index}>
                    <Td>
                      <Flex alignItems={'center'} maxW={'300px'} className={'textEllipsis'}>
                        <MyIcon name={'checkCircle'} w={'14px'} mr={1} color={'myGray.600'} />
                        {item.key}
                      </Flex>
                    </Td>
                    <Td>
                      <Box maxW={'300px'} whiteSpace={'pre-wrap'}>
                        {item.desc}
                      </Box>
                    </Td>
                    <Td>
                      {item.required ? (
                        <Flex alignItems={'center'}>
                          <MyIcon name={'check'} w={'16px'} color={'myGray.900'} mr={2} />
                        </Flex>
                      ) : (
                        '-'
                      )}
                    </Td>
                    <Td>
                      <Flex>
                        <MyIconButton
                          icon={'common/settingLight'}
                          onClick={() => {
                            setEditExtractField(item);
                          }}
                        />
                        <MyIconButton
                          icon={'delete'}
                          hoverColor={'red.500'}
                          onClick={() => {
                            onChangeNode({
                              nodeId,
                              type: 'updateInput',
                              key: NodeInputKeyEnum.extractKeys,
                              value: {
                                ...props,
                                value: extractKeys.filter((extract) => item.key !== extract.key)
                              }
                            });

                            onChangeNode({
                              nodeId,
                              type: 'delOutput',
                              key: item.key
                            });
                          }}
                        />
                      </Flex>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </TableContainer>
        </Box>
      )
    }),
    [nodeId, onChangeNode, t]
  );

  return (
    <NodeCard minW={'400px'} selected={selected} {...data}>
      {isTool && (
        <>
          <Container>
            <RenderToolInput nodeId={nodeId} inputs={inputs} />
          </Container>
        </>
      )}
      <>
        <Container>
          <IOTitle text={t('common:Input')} />
          <RenderInput
            nodeId={nodeId}
            flowInputList={commonInputs}
            CustomComponent={CustomComponent}
          />
        </Container>
      </>
      <>
        <Container>
          <IOTitle text={t('common:Output')} />
          <RenderOutput nodeId={nodeId} flowOutputList={outputs} />
        </Container>
      </>

      {!!editExtractFiled && (
        <ExtractFieldModal
          defaultField={editExtractFiled}
          onClose={() => setEditExtractField(undefined)}
          onSubmit={(data) => {
            const input = inputs.find(
              (input) => input.key === NodeInputKeyEnum.extractKeys
            ) as FlowNodeInputItemType;
            const extracts: ContextExtractAgentItemType[] = input.value || [];

            const exists = extracts.find((item) => item.key === editExtractFiled.key);

            const newInputs = exists
              ? extracts.map((item) => (item.key === editExtractFiled.key ? data : item))
              : extracts.concat(data);

            onChangeNode({
              nodeId,
              type: 'updateInput',
              key: NodeInputKeyEnum.extractKeys,
              value: {
                ...input,
                value: newInputs
              }
            });

            const newOutput: FlowNodeOutputItemType = {
              id: getNanoid(),
              key: data.key,
              label: `${t('common:extraction_results')}-${data.key}`,
              valueType: data.valueType || WorkflowIOValueTypeEnum.string,
              type: FlowNodeOutputTypeEnum.static
            };

            if (exists) {
              if (editExtractFiled.key === data.key) {
                const output = outputs.find(
                  (output) => output.key === data.key
                ) as FlowNodeOutputItemType;

                // update
                onChangeNode({
                  nodeId,
                  type: 'updateOutput',
                  key: data.key,
                  value: {
                    ...output,
                    valueType: newOutput.valueType,
                    label: newOutput.label
                  }
                });
              } else {
                onChangeNode({
                  nodeId,
                  type: 'replaceOutput',
                  key: editExtractFiled.key,
                  value: newOutput
                });
              }
            } else {
              onChangeNode({
                nodeId,
                type: 'addOutput',
                value: newOutput
              });
            }

            setEditExtractField(undefined);
          }}
        />
      )}
    </NodeCard>
  );
};

export default React.memo(NodeExtract);
