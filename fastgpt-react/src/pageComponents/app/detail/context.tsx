import {
  type Dispatch,
  type ReactNode,
  type SetStateAction,
  useCallback,
  useMemo,
  useState
} from 'react';
import { createContext } from 'use-context-selector';
import { defaultApp } from '@/web/core/app/constants';
import { delAppById, getAppDetailById, putAppById } from '@/web/core/app/api';
import { useNavigate, useLocation, useParams, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { type AppChatConfigType, type AppDetailType } from '@/packages/global/core/app/type';
import { type AppUpdateParams, type PostPublishAppProps } from '@/global/core/app/api';
import { postPublishApp, getAppLatestVersion } from '@/web/core/app/api/version';
import { useRequest2 } from '@/packages/hooks/useRequest';
import { lazy } from 'react';
import { useDisclosure } from '@chakra-ui/react';
import { useConfirm } from '@/packages/hooks/useConfirm';
import type { StoreNodeItemType } from '@/packages/global/core/workflow/type/node';
import type { StoreEdgeItemType } from '@/packages/global/core/workflow/type/edge';
import { AppErrEnum } from '@/packages/global/common/error/code/app';
import { useToast } from '@/packages/hooks/useToast';

const InfoModal = lazy(() => import('./InfoModal'));
const TagsEditModal = lazy(() => import('./TagsEditModal'));

export enum TabEnum {
  'appEdit' = 'appEdit',
  'publish' = 'publish',
  'logs' = 'logs'
}

type AppContextType = {
  appId: string;
  currentTab: TabEnum;
  route2Tab: (currentTab: TabEnum) => void;
  appDetail: AppDetailType;
  setAppDetail: Dispatch<SetStateAction<AppDetailType>>;
  loadingApp: boolean;
  updateAppDetail: (data: AppUpdateParams) => Promise<void>;
  onOpenInfoEdit: () => void;
  onOpenTeamTagModal: () => void;
  onDelApp: () => void;
  onSaveApp: (data: PostPublishAppProps) => Promise<void>;
  appLatestVersion:
    | {
        nodes: StoreNodeItemType[];
        edges: StoreEdgeItemType[];
        chatConfig: AppChatConfigType;
      }
    | undefined;
  reloadAppLatestVersion: () => void;
  reloadApp: () => void;
};

export const AppContext = createContext<AppContextType>({
  appId: '',
  currentTab: TabEnum.appEdit,
  route2Tab: function (currentTab: TabEnum): void {
    throw new Error('Function not implemented.');
  },
  appDetail: defaultApp,
  loadingApp: false,
  updateAppDetail: function (data: AppUpdateParams): Promise<void> {
    throw new Error('Function not implemented.');
  },
  setAppDetail: function (value: SetStateAction<AppDetailType>): void {
    throw new Error('Function not implemented.');
  },
  onOpenInfoEdit: function (): void {
    throw new Error('Function not implemented.');
  },
  onOpenTeamTagModal: function (): void {
    throw new Error('Function not implemented.');
  },
  onDelApp: function (): void {
    throw new Error('Function not implemented.');
  },
  onSaveApp: function (data: PostPublishAppProps): Promise<void> {
    throw new Error('Function not implemented.');
  },
  appLatestVersion: undefined,
  reloadAppLatestVersion: function (): void {
    throw new Error('Function not implemented.');
  },
  reloadApp: function (): void {
    throw new Error('Function not implemented.');
  }
});

const AppContextProvider = ({ children }: { children: ReactNode }) => {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  // 修复：从 searchParams 中读取参数，而不是从 params
  const appId = searchParams.get('appId') || '';
  const currentTab = (searchParams.get('tab') as TabEnum) || TabEnum.appEdit;

  const {
    isOpen: isOpenInfoEdit,
    onOpen: onOpenInfoEdit,
    onClose: onCloseInfoEdit
  } = useDisclosure();
  const {
    isOpen: isOpenTeamTagModal,
    onOpen: onOpenTeamTagModal,
    onClose: onCloseTeamTagModal
  } = useDisclosure();

  const route2Tab = useCallback(
    (currentTab: `${TabEnum}`) => {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set('tab', currentTab);
      navigate(`/app/detail?${newSearchParams.toString()}`);
    },
    [navigate, searchParams]
  );

  const [appDetail, setAppDetail] = useState<AppDetailType>(defaultApp);
  const { loading: loadingApp, runAsync: reloadApp } = useRequest2(
    () => {
      if (appId) {
        return getAppDetailById(appId);
      }
      return Promise.resolve(defaultApp);
    },
    {
      manual: false,
      refreshDeps: [appId],
      errorToast: t('common:core.app.error.Get app failed'),
      onError(err: any) {
        navigate('/dashboard/apps', { replace: true });
      },
      onSuccess(res) {
        setAppDetail(res);
      }
    }
  );

  const { data: appLatestVersion, run: reloadAppLatestVersion } = useRequest2(
    () => getAppLatestVersion({ appId }),
    {
      manual: false
    }
  );

  const { runAsync: updateAppDetail } = useRequest2(async (data: AppUpdateParams) => {
    await putAppById(appId, data);
    setAppDetail((state) => ({
      ...state,
      ...data,
      modules: data.nodes || state.modules
    }));
  });

  const { runAsync: onSaveApp } = useRequest2(async (data: PostPublishAppProps) => {
    try {
      await postPublishApp(appId, data);
      setAppDetail((state) => ({
        ...state,
        ...data,
        modules: data.nodes || state.modules
      }));
      reloadAppLatestVersion();
    } catch (error: any) {
      if (error.statusText == AppErrEnum.unExist) {
        return;
      }
      return Promise.reject(error);
    }
  });

  const { openConfirm: openConfirmDel, ConfirmModal: ConfirmDelModal } = useConfirm({
    content: t('app:confirm_del_app_tip', { name: appDetail.name }),
    type: 'delete'
  });
  const { runAsync: deleteApp } = useRequest2(
    async () => {
      if (!appDetail) return Promise.reject('Not load app');
      return delAppById(appDetail._id);
    },
    {
      onSuccess() {
        navigate(`/dashboard/apps`, { replace: true });
      },
      successToast: t('common:delete_success'),
      errorToast: t('common:delete_failed')
    }
  );
  const onDelApp = useCallback(
    () =>
      openConfirmDel(
        deleteApp,
        undefined,
        t('app:confirm_del_app_tip', { name: appDetail.name })
      )(),
    [appDetail.name, deleteApp, openConfirmDel, t]
  );

  const contextValue: AppContextType = useMemo(
    () => ({
      appId,
      currentTab,
      route2Tab,
      appDetail,
      setAppDetail,
      loadingApp,
      updateAppDetail,
      onOpenInfoEdit,
      onOpenTeamTagModal,
      onDelApp,
      onSaveApp,
      appLatestVersion,
      reloadAppLatestVersion,
      reloadApp
    }),
    [
      appDetail,
      appId,
      appLatestVersion,
      currentTab,
      loadingApp,
      onDelApp,
      onOpenInfoEdit,
      onOpenTeamTagModal,
      onSaveApp,
      reloadApp,
      reloadAppLatestVersion,
      route2Tab,
      updateAppDetail
    ]
  );

  return (
    <AppContext.Provider value={contextValue}>
      {children}
      {isOpenInfoEdit && <InfoModal onClose={onCloseInfoEdit} />}
      {isOpenTeamTagModal && <TagsEditModal onClose={onCloseTeamTagModal} />}

      <ConfirmDelModal />
    </AppContext.Provider>
  );
};

export default AppContextProvider;
