import React from 'react';
import { appSystemModuleTemplates } from '@/packages/global/core/workflow/template/constants';
import { useConfirm } from '@/packages/hooks/useConfirm';
import { v1Workflow2V2 } from '@/web/core/workflow/adapt';
import { WorkflowContext } from '../WorkflowComponents/context';
import { useContextSelector } from 'use-context-selector';
import { AppContext, TabEnum } from '../context';
import { useMount } from 'ahooks';
import Header from './Header';
import { Flex } from '@chakra-ui/react';
import { workflowBoxStyles } from '../constants';
import { lazy } from 'react';
import { cloneDeep } from 'lodash';
import { useTranslation } from 'react-i18next';

import Flow from '../WorkflowComponents/Flow';
import { ReactFlowCustomProvider } from '../WorkflowComponents/context/index';

const Logs = lazy(() => import('../Logs/index'));
const PublishChannel = lazy(() => import('../Publish'));

const WorkflowEdit = () => {
  const appDetail = useContextSelector(AppContext, (v) => v.appDetail);
  const currentTab = useContextSelector(AppContext, (v) => v.currentTab);

  const isV2Workflow = appDetail?.version === 'v2';
  const { t } = useTranslation();

  const { openConfirm, ConfirmModal } = useConfirm({
    showCancel: false,
    content: t('common:info.old_version_attention')
  });

  const initData = useContextSelector(WorkflowContext, (v) => v.initData);

  useMount(() => {
    if (!isV2Workflow) {
      openConfirm(() => {
        initData(JSON.parse(JSON.stringify(v1Workflow2V2((appDetail.modules || []) as any))), true);
      })();
    } else {
      initData(
        cloneDeep({
          nodes: appDetail.modules || [],
          edges: appDetail.edges || []
        }),
        true
      );
    }
  });

  return (
    <Flex {...workflowBoxStyles}>
      <Header />

      {currentTab === TabEnum.appEdit ? (
        <Flow />
      ) : (
        <Flex flexDirection={'column'} h={'100%'} px={4} pb={4}>
          {currentTab === TabEnum.publish && <PublishChannel />}
          {currentTab === TabEnum.logs && <Logs />}
        </Flex>
      )}

      {!isV2Workflow && <ConfirmModal countDown={0} />}
    </Flex>
  );
};

const Render = () => {
  return (
    <ReactFlowCustomProvider templates={appSystemModuleTemplates}>
      <WorkflowEdit />
    </ReactFlowCustomProvider>
  );
};

export default Render;
