import {LOGO_ICON} from '@/packages/global/common/system/constants';
import {useEffect, useMemo} from 'react';
import {safeAppendChild, safeDOMOperation} from '@/utils/domUtils';

const NextHead = ({ title, icon, desc }: { title?: string; icon?: string; desc?: string }) => {
  const formatIcon = useMemo(() => {
    if (!icon) return LOGO_ICON;
    if (icon.startsWith('http') || icon.startsWith('/')) {
      return icon;
    }
    return LOGO_ICON;
  }, [icon]);

  useEffect(() => {
    safeDOMOperation(() => {
      // 设置页面标题
      if (title) {
        document.title = title;
      }

      // 设置描述
      if (desc) {
        let metaDesc = document.querySelector('meta[name="description"]');
        if (!metaDesc) {
          metaDesc = document.createElement('meta');
          metaDesc.setAttribute('name', 'description');
          safeAppendChild(document.head, metaDesc);
        }
        metaDesc.setAttribute('content', desc);
      }

      // 设置图标
      if (icon) {
        let linkIcon = document.querySelector('link[rel="icon"]') as HTMLLinkElement;
        if (!linkIcon) {
          linkIcon = document.createElement('link');
          linkIcon.setAttribute('rel', 'icon');
          safeAppendChild(document.head, linkIcon);
        }
        linkIcon.href = formatIcon;
      }

      // 设置viewport（如果不存在）
      let metaViewport = document.querySelector('meta[name="viewport"]');
      if (!metaViewport) {
        metaViewport = document.createElement('meta');
        metaViewport.setAttribute('name', 'viewport');
        metaViewport.setAttribute('content', 'width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no, viewport-fit=cover');
        safeAppendChild(document.head, metaViewport);
      }

      // 设置CSP（如果不存在）
      let metaCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
      if (!metaCSP) {
        metaCSP = document.createElement('meta');
        metaCSP.setAttribute('http-equiv', 'Content-Security-Policy');
        metaCSP.setAttribute('content', 'img-src * data: blob:;');
        safeAppendChild(document.head, metaCSP);
      }
    });
  }, [title, desc, formatIcon, icon]);

  // 这个组件不需要渲染任何内容，只是用来设置head信息
  return null;
};

export default NextHead;
