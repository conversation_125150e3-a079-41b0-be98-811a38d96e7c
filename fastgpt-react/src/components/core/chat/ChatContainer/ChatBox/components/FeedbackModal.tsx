import React, {useRef} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Textare<PERSON>} from '@chakra-ui/react';
import MyModal from '@/packages/components/common/MyModal';
import {useRequest} from '@/packages/hooks/useRequest';
import {useTranslation} from 'react-i18next';
import {updateChatUserFeedback, updateShareChatUserFeedback} from '@/web/core/chat/api';
import {useContextSelector} from 'use-context-selector';
import {ChatBoxContext} from '../Provider';
import { useChatStore } from '@/web/core/chat/context/useChatStore';

const FeedbackModal = ({
  appId,
  chatId,
  dataId,
  chatType,
  onSuccess,
  onClose
}: {
  appId: string;
  chatId: string;
  dataId: string;
  chatType: string;
  onSuccess: (e: string) => void;
  onClose: () => void;
}) => {
  const ref = useRef<HTMLTextAreaElement>(null);
  const { t } = useTranslation();
  const { outLinkAuthData } = useChatStore();

  // 根据 chatType 选择使用哪个 API 函数
  const updateUserFeedbackApi = chatType === 'share' ? updateShareChatUserFeedback : updateChatUserFeedback;

  const { mutate, isLoading } = useRequest({
    mutationFn: async () => {
      const val = ref.current?.value || t('common:core.chat.feedback.No Content');
      return updateUserFeedbackApi({
        appId,
        chatId,
        dataId,
        userBadFeedback: val,
        ...outLinkAuthData
      });
    },
    onSuccess() {
      onSuccess(ref.current?.value || t('common:core.chat.feedback.No Content'));
    },
    successToast: t('common:core.chat.Feedback Success'),
    errorToast: t('common:core.chat.Feedback Failed')
  });

  return (
    <MyModal
      isOpen={true}
      onClose={onClose}
      iconSrc="/imgs/modal/badAnswer.svg"
      title={t('common:core.chat.Feedback Modal')}
    >
      <ModalBody>
        <Textarea ref={ref} rows={10} placeholder={t('common:core.chat.Feedback Modal Tip')} />
      </ModalBody>
      <ModalFooter>
        <Button variant={'whiteBase'} mr={2} onClick={onClose}>
          {t('common:Close')}
        </Button>
        <Button isLoading={isLoading} onClick={mutate}>
          {t('common:core.chat.Feedback Submit')}
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default FeedbackModal;
