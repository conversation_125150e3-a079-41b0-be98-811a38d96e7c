import { useNavigate, useLocation } from 'react-router-dom';
import { useUserStore } from '@/web/support/user/useUserStore';
import { useTranslation } from 'react-i18next';
import { useToast } from '@/packages/hooks/useToast';
import { useEffect, useState, startTransition } from 'react';

const unAuthPage: { [key: string]: boolean } = {
  '/': true,
  '/login': true,
  '/login/provider': true,
  '/login/fastlogin': true,
  '/login/sso': true,
  '/appStore': true,
  '/chat/share': true,
  '/chat/team': true,
  '/tools/price': true,
  '/price': true
};

const Auth = ({ children }: { children: JSX.Element | React.ReactNode }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const { userInfo, initUserInfo } = useUserStore();
  const [loading, setLoading] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);

  const shouldAuth = !unAuthPage[location.pathname] && !userInfo;

  useEffect(() => {
    if (!shouldAuth || isInitializing) return;

    console.log('🔐 开始用户认证...');
    setLoading(true);
    setIsInitializing(true);

    // 使用 startTransition 来包装异步操作，避免 Suspense 错误
    startTransition(() => {
      initUserInfo()
        .then((data) => {
          console.log('🔐 用户认证成功:', data);
          setLoading(false);
          setIsInitializing(false);
        })
        .catch((error) => {
          console.log('🔐 用户认证失败:', error);
          setLoading(false);
          setIsInitializing(false);
          navigate(
            `/login?lastRoute=${encodeURIComponent(location.pathname + location.search)}`
          );
          toast({
            status: 'warning',
            title: t('common:support.user.Need to login')
          });
        });
    });
  }, [shouldAuth, isInitializing, initUserInfo, navigate, location.pathname, location.search, t, toast]);

  // 显示条件：已登录用户 或 不需要认证的页面
  if (userInfo || unAuthPage[location.pathname] === true) {
    return children;
  }

  // 正在认证中，返回null等待认证完成
  return null;
};

export default Auth;
