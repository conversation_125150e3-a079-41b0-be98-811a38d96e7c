import React, { useMemo, Suspense } from 'react';
import { Box, Flex } from '@chakra-ui/react';
import { useNavigate, useLocation, useSearchParams } from "react-router-dom";
import { useLoading } from '@/packages/hooks/useLoading';
import { useSystemStore } from '@/web/common/system/useSystemStore';
import { useQuery } from '@tanstack/react-query';
import { useUserStore } from '@/web/support/user/useUserStore';
import { getUnreadCount } from '@/web/support/user/inform/api';
import { lazy } from 'react';
import { useI18nLng } from '@/packages/hooks/useI18n';

import Auth from './auth';
import { useSystem } from '@/packages/hooks/useSystem';
import { useDebounceEffect, useMount } from 'ahooks';
import { useTranslation } from 'react-i18next';
import { useToast } from '@/packages/hooks/useToast';
import WorkorderButton from './WorkorderButton';

const Navbar = lazy(() => import('./navbar'));
const NavbarPhone = lazy(() => import('./navbarPhone'));

const ResetExpiredPswModal = lazy(
  () => import('@/components/support/user/safe/ResetExpiredPswModal')
);
const NotSufficientModal = lazy(() => import('@/components/support/wallet/NotSufficientModal'));
const SystemMsgModal = lazy(() => import('@/components/support/user/inform/SystemMsgModal'));
const ImportantInform = lazy(() => import('@/components/support/user/inform/ImportantInform'));
const UpdateContact = lazy(() => import('@/components/support/user/inform/UpdateContactModal'));
const ManualCopyModal = lazy(
  () => import('@/packages/hooks/useCopyData').then((mod) => ({ default: mod.ManualCopyModal }))
);

const pcUnShowLayoutRoute: Record<string, boolean> = {
  '/': true,
  '/login': true,
  '/login/provider': true,
  '/login/fastlogin': true,
  '/chat/share': true,
  '/chat/team': true,
  '/app/edit': true,
  '/chat': true,
  '/tools/price': true,
  '/price': true,
  '/dashboard/apps': true,
  '/dataset/list': true,
  '/account/model': true,
  '/account/apikey': true
};
const phoneUnShowLayoutRoute: Record<string, boolean> = {
  '/': true,
  '/login': true,
  '/login/provider': true,
  '/login/fastlogin': true,
  '/chat/share': true,
  '/chat/team': true,
  '/tools/price': true,
  '/price': true
};

export const navbarWidth = '64px';

const Layout = ({ children }: { children: JSX.Element }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const { toast } = useToast();
  const { t } = useTranslation();
  const { Loading } = useLoading();
  const { loading, feConfigs, llmModelList, embeddingModelList } = useSystemStore();
  const { isPc } = useSystem();
  const { userInfo, isUpdateNotification, setIsUpdateNotification } = useUserStore();
  const { setUserDefaultLng } = useI18nLng();

  const isChatPage = useMemo(
    () => location.pathname === '/chat' && searchParams.toString().length !== 0,
    [location.pathname, searchParams]
  );
  const isHideNavbar = true; //!!pcUnShowLayoutRoute[location.pathname];

  // System hook
  const { data, refetch: refetchUnRead } = useQuery(['getUnreadCount'], getUnreadCount, {
    enabled: false,
    refetchInterval: 30000
  });
  const unread = data?.unReadCount || 0;
  const importantInforms = data?.importantInforms || [];

  const showUpdateNotification =
    isUpdateNotification &&
    feConfigs?.bind_notification_method &&
    feConfigs?.bind_notification_method.length > 0 &&
    !userInfo?.contact &&
    !!userInfo?.team.permission.isOwner;

  useMount(() => {
    setUserDefaultLng();
  });

  // Check model invalid
  useDebounceEffect(
    () => {
      console.log('🔍 Layout模型检查开始:', {
        username: userInfo?.username,
        llmModelList: llmModelList,
        llmModelListLength: llmModelList.length,
        embeddingModelList: embeddingModelList,
        embeddingModelListLength: embeddingModelList.length,
        currentPath: location.pathname
      });
      
      if (userInfo?.username === 'root') {
        if (llmModelList.length === 0) {
          console.log('❌ LLM模型列表为空，显示警告并重定向');
          toast({
            status: 'warning',
            title: t('common:llm_model_not_config')
          });
          location.pathname !== '/account/model' && navigate('/account/model');
        } else if (embeddingModelList.length === 0) {
          console.log('❌ Embedding模型列表为空，显示警告并重定向');
          toast({
            status: 'warning',
            title: t('common:embedding_model_not_config')
          });
          location.pathname !== '/account/model' && navigate('/account/model');
        } else {
          console.log('✅ 模型检查通过，无需重定向');
        }
      } else {
        console.log('ℹ️ 非root用户，跳过模型检查');
      }
    },
    [embeddingModelList.length, llmModelList.length, userInfo?.username],
    {
      wait: 2000
    }
  );

  return (
    <Suspense fallback={<Box>Loading...</Box>}>
      <Box h={'100%'}  bg={'myGray.100'}>
        {isPc === true && (
          <>
            {isHideNavbar ? (
              <Auth>{children}</Auth>
            ) : (
              <Auth>
                <Box h={'100%'} position={'fixed'} left={0} top={0} w={navbarWidth}>
                  <Navbar unread={unread} />
                </Box>
                <Box h={'100%'} ml={navbarWidth} overflow={'overlay'}>
                  {children}
                </Box>
              </Auth>
            )}
          </>
        )}
        {isPc === false && (
          <>
            {phoneUnShowLayoutRoute[location.pathname] || isChatPage ? (
              <Auth>{children}</Auth>
            ) : (
              <Auth>
                <Flex h={'100%'} flexDirection={'column'}>
                  <Box flex={'1 0 0'} h={0}>
                    {children}
                  </Box>
                  <Box h={'50px'} borderTop={'1px solid rgba(0,0,0,0.1)'}>
                    <NavbarPhone unread={unread} />
                  </Box>
                </Flex>
              </Auth>
            )}
          </>
        )}
      </Box>
      {feConfigs?.isPlus && (
        <>
          <NotSufficientModal />
          {/*<SystemMsgModal />*/}
          {showUpdateNotification && (
            <UpdateContact onClose={() => setIsUpdateNotification(false)} mode="contact" />
          )}
          {!!userInfo && importantInforms.length > 0 && (
            <ImportantInform informs={importantInforms} refetch={refetchUnRead} />
          )}
          <ResetExpiredPswModal />
          <WorkorderButton />
        </>
      )}

      <ManualCopyModal />
      <Loading loading={loading} zIndex={999999} />
    </Suspense>
  );
};

export default Layout;
