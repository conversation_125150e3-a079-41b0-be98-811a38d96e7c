import React, { useMemo } from 'react';
import { Box, type BoxProps, Flex, Link as ChakraLink, type LinkProps } from '@chakra-ui/react';
import { useNavigate, useLocation, useSearchParams, Link } from "react-router-dom";
import { useUserStore } from '@/web/support/user/useUserStore';
import { useChatStore } from '@/web/core/chat/context/useChatStore';
import { HUMAN_ICON } from '@/packages/global/common/system/constants';
import Badge from '../Badge';
import Avatar from '@/packages/components/common/Avatar';
import MyIcon from '@/packages/components/common/Icon';
import { useTranslation } from 'react-i18next';
import { useSystemStore } from '@/web/common/system/useSystemStore';
import MyTooltip from '@/packages/components/common/MyTooltip';

export enum NavbarTypeEnum {
  normal = 'normal',
  small = 'small'
}

const itemStyles: BoxProps & LinkProps = {
  my: 2,
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  cursor: 'pointer',
  w: '48px',
  h: '58px',
  borderRadius: 'md'
};
const hoverStyle: LinkProps = {
  _hover: {
    bg: 'myGray.05',
    color: 'primary.600'
  }
};

const Navbar = ({ unread }: { unread: number }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { userInfo } = useUserStore();
  const { gitStar, feConfigs } = useSystemStore();
  const { lastChatAppId } = useChatStore();

  const navbarList = useMemo(
    () => [
      {
        label: t('common:navbar.Chat'),
        icon: 'core/chat/chatLight',
        activeIcon: 'core/chat/chatFill',
        link: `/chat?appId=${lastChatAppId}`,
        activeLink: ['/chat']
      },
      {
        label: t('common:navbar.Studio'),
        icon: 'core/app/aiLight',
        activeIcon: 'core/app/aiFill',
        link: `/dashboard/apps`,
        activeLink: [
          '/dashboard/apps',
          '/app/detail',
          '/dashboard/templateMarket',
          '/dashboard/[pluginGroupId]',
          '/dashboard/mcpServer'
        ]
      },
      {
        label: t('common:navbar.Datasets'),
        icon: 'core/dataset/datasetLight',
        activeIcon: 'core/dataset/datasetFill',
        link: `/dataset/list`,
        activeLink: ['/dataset/list', '/dataset/detail']
      },
      {
        label: t('common:navbar.Account'),
        icon: 'support/user/userLight',
        activeIcon: 'support/user/userFill',
        link: '/account/info',
        activeLink: [
          '/account/bill',
          '/account/info',
          '/account/team',
          '/account/usage',
          '/account/thirdParty',
          '/account/apikey',
          '/account/setting',
          '/account/inform',
          '/account/promotion',
          '/account/model'
        ]
      }
    ],
    [lastChatAppId, t]
  );

  const isSecondNavbarPage = useMemo(() => {
    return ['/toolkit'].includes(location.pathname);
  }, [location.pathname]);

  return (
    <Flex
      flexDirection={'column'}
      alignItems={'center'}
      pt={6}
      h={'100%'}
      w={'100%'}
      userSelect={'none'}
      pb={2}
      bg={isSecondNavbarPage ? 'myGray.50' : 'transparent'}
    >
      {/* logo */}
      <Box
        flex={'0 0 auto'}
        mb={3}
        border={'2px solid #fff'}
        borderRadius={'50%'}
        overflow={'hidden'}
        cursor={'pointer'}
        onClick={() => navigate('/account/info')}
      >
        <Avatar w={'2rem'} h={'2rem'} src={userInfo?.avatar} borderRadius={'50%'} />
      </Box>
      {/* 导航列表 */}
      <Box flex={1}>
        {navbarList.map((item) => {
          const isActive = item.activeLink.includes(location.pathname);

          return (
            <Box
              key={item.link}
              {...itemStyles}
              {...(isActive
                ? {
                    bg: 'white',
                    boxShadow:
                      '0px 0px 1px 0px rgba(19, 51, 107, 0.08), 0px 4px 4px 0px rgba(19, 51, 107, 0.05)'
                  }
                : {
                    bg: 'transparent',
                    _hover: {
                      bg: isSecondNavbarPage ? 'white' : 'rgba(255,255,255,0.9)'
                    }
                  })}
              {...(item.link !== location.pathname + location.search
                ? {
                    onClick: () => navigate(item.link)
                  }
                : {})}
            >
              <MyIcon
                {...(isActive
                  ? {
                      name: item.activeIcon as any,
                      color: 'primary.600'
                    }
                  : {
                      name: item.icon as any,
                      color: 'myGray.400'
                    })}
                width={'20px'}
                height={'20px'}
              />
              <Box
                fontSize={'12px'}
                transform={'scale(0.9)'}
                mt={'5px'}
                lineHeight={1}
                color={isActive ? 'primary.700' : 'myGray.500'}
              >
                {item.label}
              </Box>
            </Box>
          );
        })}
      </Box>

      {unread > 0 && (
        <Box>
          <Link
            to={`/account/inform`}
            style={{ textDecoration: 'none' }}
          >
            <Box
              {...itemStyles}
              {...hoverStyle}
              mb={0}
              color={'myGray.500'}
              height={'48px'}
            >
              <Badge count={unread}>
                <MyIcon name={'support/user/informLight'} width={'22px'} height={'22px'} />
              </Badge>
            </Box>
          </Link>
        </Box>
      )}

      {feConfigs?.navbarItems
        ?.filter((item) => item.isActive)
        .map((item) => (
          <MyTooltip key={item.id} label={item.name} placement={'right-end'}>
            <ChakraLink
              href={item.url}
              target={'_blank'}
              {...itemStyles}
              {...hoverStyle}
              mt={0}
              color={'myGray.400'}
              height={'48px'}
            >
              <Avatar src={item.avatar} borderRadius={'md'} width={'26px'} height={'26px'} />
            </ChakraLink>
          </MyTooltip>
        ))}

      {feConfigs?.show_git && (
        <MyTooltip label={`Git Star: ${gitStar}`} placement={'right-end'}>
          <ChakraLink
            href="https://github.com/labring/FastGPT"
            target={'_blank'}
            {...itemStyles}
            {...hoverStyle}
            mt={0}
            color={'myGray.400'}
            height={'48px'}
          >
            <MyIcon name={'common/gitInlight'} width={'26px'} height={'26px'} />
          </ChakraLink>
        </MyTooltip>
      )}
    </Flex>
  );
};

export default Navbar;
