.contentEditable {
  position: relative;
  height: 100%;
  width: 100%;
  border: 1px solid rgb(232, 235, 240);
  border-radius: var(--chakra-radii-md);
  padding: 8px 12px;
  // background: #fff;

  font-size: var(--chakra-fontSizes-sm);
  overflow-y: auto;

  &:hover {
    border-color: var(--chakra-colors-primary-300);
  }
  &::-webkit-scrollbar {
    color: var(--chakra-colors-myGray-100);
  }
  &::-webkit-scrollbar-thumb {
    background-color: var(--chakra-colors-myGray-200) !important;
    cursor: pointer;
  }
  &::-webkit-scrollbar-thumb:hover {
    background-color: var(--chakra-colors-myGray-250) !important;
  }
}

.contentEditable:focus {
  outline: none;
  border: 1px solid;
  border-color: var(--chakra-colors-primary-600);
  box-shadow: 0px 0px 0px 2.4px rgba(51, 112, 255, 0.15);
}

.variable {
  color: var(--chakra-colors-primary-600);
  padding: 0 2px;
}
