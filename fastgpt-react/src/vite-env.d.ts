/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_BACKEND_URL: string
  readonly VITE_JAVA_BACKEND_URL: string
  readonly VITE_LAF_ENV: string
  readonly VITE_FASTGPT_PRO_URL: string
  readonly VITE_SYSTEM_TITLE: string
  readonly VITE_SYSTEM_DESCRIPTION: string
  readonly VITE_SYSTEM_FAVICON: string
  readonly VITE_BASE_URL: string
  readonly VITE_CHINESE_IP_REDIRECT_URL: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
} 