{"compilerOptions": {"target": "esnext", "useDefineForClassFields": true, "allowJs": true, "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["esnext", "dom"], "skipLibCheck": true, "types": ["element-plus/global", "sinitek-ui/global", "sinitek-workflow/global", "sirmapp/global"], "baseUrl": "./", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "references": [{"path": "./tsconfig.node.json"}]}